# 律师推荐模块流程图

## 1. 律师推荐系统整体架构流程图

```mermaid
graph TB
    subgraph "用户交互层"
        U1[用户描述法律需求]
        U2[选择专业领域]
        U3[设置筛选条件]
        U4[查看推荐结果]
        U5[联系律师]
    end
    
    subgraph "律师推荐智能体"
        LRA1[接收用户需求]
        LRA2[需求分析处理]
        LRA3[调用推荐算法]
        LRA4[结果排序优化]
        LRA5[格式化输出]
    end
    
    subgraph "需求分析模块"
        RA1[文本预处理]
        RA2[关键词提取]
        RA3[专业领域识别]
        RA4[紧急程度评估]
        RA5[案件复杂度分析]
    end
    
    subgraph "律师数据获取"
        LDA1[LawyerDataAPI数据接口]
        LDA2[Web搜索引擎]
        LDA3[律师网站爬取]
        LDA4[法律目录检索]
        LDA5[数据去重处理]
        LDA6[数据质量验证]
    end
    
    subgraph "推荐算法引擎"
        RE1[专业匹配算法]
        RE2[经验评分算法]
        RE3[口碑评价算法]
        RE4[地理位置算法]
        RE5[费用评估算法]
        RE6[综合评分计算]
    end
    
    subgraph "数据存储层"
        DS1[律师基础信息]
        DS2[专业领域标签]
        DS3[案例成功率]
        DS4[用户评价数据]
        DS5[地理位置信息]
        DS6[收费标准数据]
    end
    
    subgraph "外部数据源"
        EDS1[律师协会网站]
        EDS2[法律服务平台]
        EDS3[律师事务所官网]
        EDS4[法院判决书网站]
        EDS5[律师评价网站]
    end
    
    %% 主流程连接
    U1 --> LRA1
    U2 --> LRA1
    U3 --> LRA1
    LRA1 --> LRA2
    LRA2 --> RA1
    
    %% 需求分析流程
    RA1 --> RA2
    RA2 --> RA3
    RA3 --> RA4
    RA4 --> RA5
    RA5 --> LRA3
    
    %% 数据获取流程
    LRA3 --> LDA1
    LDA1 --> LDA2
    LDA2 --> LDA3
    LDA3 --> LDA4
    LDA4 --> LDA5
    LDA5 --> LDA6
    
    %% 推荐算法流程
    LDA6 --> RE1
    RA3 --> RE1
    RE1 --> RE2
    RE2 --> RE3
    RE3 --> RE4
    RE4 --> RE5
    RE5 --> RE6
    RE6 --> LRA4
    
    %% 数据存储连接
    LDA6 --> DS1
    DS1 --> DS2
    DS2 --> DS3
    DS3 --> DS4
    DS4 --> DS5
    DS5 --> DS6
    
    %% 外部数据源连接
    EDS1 --> LDA2
    EDS2 --> LDA3
    EDS3 --> LDA3
    EDS4 --> LDA4
    EDS5 --> LDA4
    
    %% 结果输出
    LRA4 --> LRA5
    LRA5 --> U4
    U4 --> U5
    
    %% 样式定义
    classDef userClass fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef agentClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef analysisClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef algorithmClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef storageClass fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef externalClass fill:#fafafa,stroke:#616161,stroke-width:2px
    
    class U1,U2,U3,U4,U5 userClass
    class LRA1,LRA2,LRA3,LRA4,LRA5 agentClass
    class RA1,RA2,RA3,RA4,RA5 analysisClass
    class LDA1,LDA2,LDA3,LDA4,LDA5,LDA6 dataClass
    class RE1,RE2,RE3,RE4,RE5,RE6 algorithmClass
    class DS1,DS2,DS3,DS4,DS5,DS6 storageClass
    class EDS1,EDS2,EDS3,EDS4,EDS5 externalClass
```

## 2. 律师推荐算法详细流程图

```mermaid
flowchart TD
    Start([开始律师推荐]) --> Input[接收用户需求描述]
    
    Input --> Parse[需求文本解析]
    Parse --> Extract[提取关键信息]
    
    subgraph "需求分析"
        Extract --> Field[识别专业领域]
        Field --> Urgency[评估紧急程度]
        Urgency --> Complexity[分析案件复杂度]
        Complexity --> Location[确定地理位置]
        Location --> Budget[预算范围评估]
    end
    
    Budget --> GetLawyers[获取律师数据]
    
    subgraph "数据获取"
        GetLawyers --> WebSearch[Web搜索律师信息]
        WebSearch --> ScrapeData[爬取律师网站数据]
        ScrapeData --> ValidateData[数据验证和清洗]
        ValidateData --> DeduplicateData[去重处理]
    end
    
    DeduplicateData --> StartScoring[开始评分计算]
    
    subgraph "评分算法"
        StartScoring --> SpecialtyScore[专业匹配评分]
        SpecialtyScore --> ExperienceScore[经验评分]
        ExperienceScore --> ReputationScore[口碑评分]
        ReputationScore --> LocationScore[地理位置评分]
        LocationScore --> CostScore[费用评分]
        CostScore --> WeightedSum[加权综合评分]
    end
    
    WeightedSum --> Sort[按评分排序]
    Sort --> Filter[应用筛选条件]
    Filter --> TopN[选择Top-N律师]
    
    subgraph "结果优化"
        TopN --> Diversify[结果多样化处理]
        Diversify --> AddDetails[补充详细信息]
        AddDetails --> FormatOutput[格式化输出]
    end
    
    FormatOutput --> Return[返回推荐结果]
    Return --> End([结束])
    
    %% 决策节点
    GetLawyers --> HasCache{是否有缓存数据?}
    HasCache -->|是| UseCache[使用缓存数据]
    HasCache -->|否| WebSearch
    UseCache --> StartScoring
    
    Filter --> EnoughResults{结果数量足够?}
    EnoughResults -->|是| TopN
    EnoughResults -->|否| ExpandSearch[扩大搜索范围]
    ExpandSearch --> GetLawyers
    
    %% 样式定义
    classDef processClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef algorithmClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef startEndClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    
    class Start,End startEndClass
    class HasCache,EnoughResults decisionClass
    class GetLawyers,WebSearch,ScrapeData,ValidateData,DeduplicateData,UseCache,ExpandSearch dataClass
    class SpecialtyScore,ExperienceScore,ReputationScore,LocationScore,CostScore,WeightedSum algorithmClass
```

## 3. 律师推荐系统时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端界面
    participant API as Flask API
    participant LRAgent as 律师推荐智能体
    participant DataAPI as 律师数据API
    participant WebSearch as Web搜索引擎
    participant Algorithm as 推荐算法
    participant Database as 数据库
    
    User->>Frontend: 1. 输入法律需求
    Frontend->>API: 2. 发送推荐请求
    API->>LRAgent: 3. 调用律师推荐智能体
    
    LRAgent->>LRAgent: 4. 解析用户需求
    LRAgent->>Database: 5. 检查缓存数据
    
    alt 缓存未命中
        LRAgent->>DataAPI: 6. 请求律师数据
        DataAPI->>WebSearch: 7. 搜索律师信息
        WebSearch-->>DataAPI: 8. 返回搜索结果
        DataAPI->>DataAPI: 9. 数据清洗和验证
        DataAPI-->>LRAgent: 10. 返回律师数据
        LRAgent->>Database: 11. 缓存律师数据
    else 缓存命中
        Database-->>LRAgent: 6. 返回缓存数据
    end
    
    LRAgent->>Algorithm: 12. 调用推荐算法
    Algorithm->>Algorithm: 13. 计算专业匹配度
    Algorithm->>Algorithm: 14. 计算经验评分
    Algorithm->>Algorithm: 15. 计算口碑评分
    Algorithm->>Algorithm: 16. 综合评分排序
    Algorithm-->>LRAgent: 17. 返回推荐结果
    
    LRAgent->>LRAgent: 18. 格式化输出结果
    LRAgent-->>API: 19. 返回推荐律师列表
    API-->>Frontend: 20. 返回JSON响应
    Frontend-->>User: 21. 展示推荐律师
    
    User->>Frontend: 22. 选择律师查看详情
    Frontend->>API: 23. 请求律师详细信息
    API->>Database: 24. 查询律师详情
    Database-->>API: 25. 返回详细信息
    API-->>Frontend: 26. 返回律师详情
    Frontend-->>User: 27. 展示律师详细信息
    
    Note over User,Database: 完整的律师推荐流程
```

## 4. 推荐算法核心逻辑

### 4.1 多因子评分算法

```python
def recommend_lawyers(user_requirement, filters=None):
    """
    律师推荐主算法
    """
    # 1. 需求分析
    analyzed_requirement = analyze_requirement(user_requirement)
    
    # 2. 获取律师数据
    lawyers = get_lawyer_data(analyzed_requirement.field)
    
    # 3. 计算评分
    scored_lawyers = []
    for lawyer in lawyers:
        # 专业匹配评分 (权重: 40%)
        specialty_score = calculate_specialty_match(
            analyzed_requirement.field, 
            lawyer.specialties
        )
        
        # 经验评分 (权重: 30%)
        experience_score = calculate_experience_score(
            lawyer.years_of_practice,
            lawyer.case_count,
            lawyer.success_rate
        )
        
        # 口碑评分 (权重: 20%)
        reputation_score = calculate_reputation_score(
            lawyer.rating,
            lawyer.review_count,
            lawyer.client_feedback
        )
        
        # 地理位置评分 (权重: 10%)
        location_score = calculate_location_score(
            analyzed_requirement.location,
            lawyer.location
        )
        
        # 综合评分
        total_score = (
            specialty_score * 0.4 +
            experience_score * 0.3 +
            reputation_score * 0.2 +
            location_score * 0.1
        )
        
        scored_lawyers.append((lawyer, total_score))
    
    # 4. 排序和筛选
    scored_lawyers.sort(key=lambda x: x[1], reverse=True)
    
    # 5. 返回Top-N结果
    return [lawyer for lawyer, score in scored_lawyers[:10]]
```

### 4.2 专业匹配算法

```python
def calculate_specialty_match(required_field, lawyer_specialties):
    """
    计算专业匹配度
    """
    # 直接匹配
    if required_field in lawyer_specialties:
        return 1.0
    
    # 相关专业匹配
    field_similarity_map = {
        '婚姻纠纷': ['家庭法', '民事诉讼', '财产分割'],
        '合同纠纷': ['商事诉讼', '民事诉讼', '经济法'],
        '工伤赔偿': ['劳动法', '人身损害', '社会保险']
    }
    
    related_fields = field_similarity_map.get(required_field, [])
    max_similarity = 0.0
    
    for specialty in lawyer_specialties:
        if specialty in related_fields:
            max_similarity = max(max_similarity, 0.8)
        else:
            # 使用文本相似度计算
            similarity = calculate_text_similarity(required_field, specialty)
            max_similarity = max(max_similarity, similarity * 0.6)
    
    return max_similarity
```

## 5. 技术特色

### 5.1 智能需求分析
- **自然语言处理**：理解用户的法律需求描述
- **关键词提取**：识别案件类型、紧急程度、地理位置等
- **语义理解**：分析案件复杂度和专业要求

### 5.2 多源数据整合
- **律师协会数据**：获取官方认证的律师信息
- **法律服务平台**：整合多个平台的律师资料
- **网络爬虫技术**：实时获取最新的律师信息
- **数据质量控制**：去重、验证、清洗确保数据准确性

### 5.3 智能推荐算法
- **多因子评分**：专业匹配、经验、口碑、位置、费用综合评估
- **权重优化**：根据用户需求动态调整各因子权重
- **结果多样化**：避免推荐结果过于单一
- **个性化推荐**：基于用户历史和偏好优化推荐

### 5.4 系统优化
- **缓存机制**：提高响应速度，减少重复计算
- **异步处理**：支持大量并发用户请求
- **实时更新**：律师信息实时同步更新
- **容错处理**：确保系统稳定性和可靠性
