<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LCA 法律咨询助手 - 项目答辩</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section h2 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section h3 {
            color: #764ba2;
            font-size: 1.5rem;
            margin: 20px 0 15px 0;
            border-left: 4px solid #667eea;
            padding-left: 15px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .card {
            background: #f8f9ff;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .card:hover {
            background: #f0f2ff;
            transform: translateX(5px);
        }

        .card h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }

        .tech-tag {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .architecture-diagram {
            background: #f8f9ff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .flow-step {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 5px;
            position: relative;
        }

        .flow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            color: #667eea;
            font-weight: bold;
        }

        .flow-step:last-child::after {
            display: none;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-item {
            text-align: center;
            background: #667eea;
            color: white;
            padding: 20px;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list i {
            color: #667eea;
            width: 20px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 20px;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }

        .nav-menu {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .nav-menu a {
            display: block;
            color: #667eea;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .nav-menu a:hover {
            background: #f0f2ff;
        }
    </style>
</head>
<body>
    <div class="nav-menu">
        <a href="#background">项目背景</a>
        <a href="#introduction">项目介绍</a>
        <a href="#architecture">技术架构</a>
        <a href="#features">核心功能</a>
        <a href="#extensions">拓展功能</a>
        <a href="#technology">核心技术</a>
    </div>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-balance-scale"></i> LCA 法律咨询助手</h1>
            <p>基于大语言模型的智能法律咨询系统</p>
        </div>

        <section id="background" class="section">
            <h2><i class="fas fa-exclamation-triangle"></i> 项目背景</h2>
            
            <h3>现实痛点</h3>
            <div class="grid">
                <div class="card">
                    <h4><i class="fas fa-dollar-sign"></i> 法律咨询门槛高</h4>
                    <p>专业律师咨询费用高昂，一般咨询费用300-1000元/小时，普通民众难以承受。</p>
                </div>
                <div class="card">
                    <h4><i class="fas fa-clock"></i> 时间地域限制</h4>
                    <p>律师工作时间有限，优质法律资源集中在大城市，偏远地区服务匮乏。</p>
                </div>
                <div class="card">
                    <h4><i class="fas fa-book"></i> 法律信息获取困难</h4>
                    <p>法律条文晦涩难懂，信息分散，更新滞后，普通民众难以理解。</p>
                </div>
                <div class="card">
                    <h4><i class="fas fa-users"></i> 场景化需求增长</h4>
                    <p>婚姻纠纷、合同纠纷、工伤赔偿等场景化法律咨询需求日益增长。</p>
                </div>
            </div>

            <h3>解决方案</h3>
            <div class="highlight">
                <h4><i class="fas fa-robot"></i> 智能化法律咨询平台</h4>
                <ul class="feature-list">
                    <li><i class="fas fa-clock"></i> 24/7全天候服务，随时提供法律咨询</li>
                    <li><i class="fas fa-dollar-sign"></i> 大幅降低法律咨询成本，普惠法律服务</li>
                    <li><i class="fas fa-brain"></i> 基于大模型训练，提供专业准确的法律建议</li>
                    <li><i class="fas fa-cogs"></i> 场景化服务，针对常见法律场景专门优化</li>
                </ul>
            </div>
        </section>

        <section id="introduction" class="section">
            <h2><i class="fas fa-info-circle"></i> 项目介绍</h2>
            
            <div class="highlight">
                <p><strong>LCA (Legal Consultation Assistant)</strong> 是一款基于大语言模型的智能法律咨询系统，旨在为用户提供专业、便捷、低成本的法律咨询服务。</p>
            </div>

            <h3>项目优势</h3>
            <div class="grid">
                <div class="card">
                    <h4><i class="fas fa-microchip"></i> 技术优势</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> 基于千问大模型，强大的NLP能力</li>
                        <li><i class="fas fa-check"></i> 多模态交互，支持文字和语音</li>
                        <li><i class="fas fa-check"></i> 智能会话管理，支持多轮对话</li>
                        <li><i class="fas fa-check"></i> 模块化架构，易于扩展维护</li>
                    </ul>
                </div>
                <div class="card">
                    <h4><i class="fas fa-service"></i> 服务优势</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> 专业性强，法律领域深度优化</li>
                        <li><i class="fas fa-check"></i> 场景化服务，针对高频场景</li>
                        <li><i class="fas fa-check"></i> 个性化推荐，智能律师匹配</li>
                        <li><i class="fas fa-check"></i> 文书生成，自动化提高效率</li>
                    </ul>
                </div>
            </div>

            <h3>项目亮点</h3>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">6</span>
                    <span>核心功能模块</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <span>专业法律场景</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span>全天候服务</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">多模态</span>
                    <span>交互方式</span>
                </div>
            </div>
        </section>

        <section id="architecture" class="section">
            <h2><i class="fas fa-sitemap"></i> 技术架构</h2>
            
            <h3>系统架构图</h3>
            <div class="architecture-diagram">
                <div style="margin: 20px 0;">
                    <div class="flow-step">前端层 (Web界面)</div>
                    <div class="flow-step">API层 (Flask)</div>
                    <div class="flow-step">智能体层 (Agents)</div>
                    <div class="flow-step">服务层 (Services)</div>
                    <div class="flow-step">模型层 (AI Models)</div>
                </div>
            </div>

            <h3>技术栈</h3>
            <div class="grid">
                <div class="card">
                    <h4>后端技术</h4>
                    <div class="tech-stack">
                        <span class="tech-tag">Python 3.8+</span>
                        <span class="tech-tag">Flask</span>
                        <span class="tech-tag">LangChain</span>
                        <span class="tech-tag">千问大模型</span>
                        <span class="tech-tag">DashScope SDK</span>
                    </div>
                </div>
                <div class="card">
                    <h4>前端技术</h4>
                    <div class="tech-stack">
                        <span class="tech-tag">HTML5/CSS3</span>
                        <span class="tech-tag">JavaScript ES6+</span>
                        <span class="tech-tag">Font Awesome</span>
                        <span class="tech-tag">响应式设计</span>
                    </div>
                </div>
            </div>

            <h3>核心设计模式</h3>
            <div class="code-block">
# 智能体基类设计
class AgentBase(ABC):
    def __init__(self, name, prompt_file, session_id=None):
        self.name = name
        self.prompt_file = prompt_file
        self.session_id = session_id
        self.create_chatbot()
    
    @abstractmethod
    def chat_with_history(self, user_input, session_id=None):
        """处理用户输入，生成回复"""
        pass
            </div>
        </section>

        <section id="features" class="section">
            <h2><i class="fas fa-cogs"></i> 核心功能</h2>

            <div class="grid">
                <div class="card">
                    <h4><i class="fas fa-balance-scale"></i> 场景选择</h4>
                    <p><strong>专业场景化咨询服务</strong></p>
                    <ul class="feature-list">
                        <li><i class="fas fa-heart"></i> 婚姻纠纷：离婚程序、财产分割、子女抚养</li>
                        <li><i class="fas fa-file-contract"></i> 合同纠纷：合同效力、违约责任、争议解决</li>
                        <li><i class="fas fa-hard-hat"></i> 工伤赔偿：工伤认定、伤残鉴定、赔偿计算</li>
                    </ul>
                </div>

                <div class="card">
                    <h4><i class="fas fa-question-circle"></i> 法律问答</h4>
                    <p><strong>通用法律知识问答</strong></p>
                    <ul class="feature-list">
                        <li><i class="fas fa-book-open"></i> 广泛覆盖各个法律领域</li>
                        <li><i class="fas fa-brain"></i> 基于大模型的专业回答</li>
                        <li><i class="fas fa-comments"></i> 支持多轮连续对话</li>
                        <li><i class="fas fa-bolt"></i> 实时流式响应</li>
                    </ul>
                </div>

                <div class="card">
                    <h4><i class="fas fa-search"></i> 案例检索</h4>
                    <p><strong>智能法律案例搜索</strong></p>
                    <ul class="feature-list">
                        <li><i class="fas fa-language"></i> 自然语言查询支持</li>
                        <li><i class="fas fa-vector-square"></i> 基于语义相似度匹配</li>
                        <li><i class="fas fa-filter"></i> 多维度筛选功能</li>
                        <li><i class="fas fa-list-alt"></i> 详细案例信息展示</li>
                    </ul>
                </div>

                <div class="card">
                    <h4><i class="fas fa-graduation-cap"></i> 法律学习</h4>
                    <p><strong>系统化法律知识学习</strong></p>
                    <ul class="feature-list">
                        <li><i class="fas fa-book"></i> 宪法、刑法、民法典学习</li>
                        <li><i class="fas fa-chalkboard-teacher"></i> 互动式教学方式</li>
                        <li><i class="fas fa-chart-line"></i> 学习进度跟踪</li>
                        <li><i class="fas fa-user-cog"></i> 个性化内容推荐</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="extensions" class="section">
            <h2><i class="fas fa-plus-circle"></i> 拓展功能</h2>

            <div class="grid">
                <div class="card">
                    <h4><i class="fas fa-microphone"></i> 语音交互模块</h4>
                    <p><strong>完整的语音对话体验</strong></p>
                    <div class="highlight">
                        <p>基于阿里云通义千问语音服务</p>
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-microphone-alt"></i> 多格式语音识别 (WAV/MP3/WebM)</li>
                        <li><i class="fas fa-volume-up"></i> 多音色语音合成</li>
                        <li><i class="fas fa-sync-alt"></i> 实时语音交互</li>
                        <li><i class="fas fa-mobile-alt"></i> 移动端优化支持</li>
                    </ul>
                </div>

                <div class="card">
                    <h4><i class="fas fa-user-tie"></i> 律师推荐模块</h4>
                    <p><strong>智能律师匹配系统</strong></p>
                    <div class="highlight">
                        <p>基于多因子推荐算法</p>
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-bullseye"></i> 专业领域智能匹配</li>
                        <li><i class="fas fa-star"></i> 多维度能力评估</li>
                        <li><i class="fas fa-info-circle"></i> 详细律师信息展示</li>
                        <li><i class="fas fa-phone"></i> 直接联系方式提供</li>
                    </ul>
                </div>

                <div class="card">
                    <h4><i class="fas fa-file-alt"></i> 文书生成模块</h4>
                    <p><strong>智能法律文书生成</strong></p>
                    <div class="highlight">
                        <p>支持多种常用法律文书模板</p>
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-gavel"></i> 民事诉讼类文书</li>
                        <li><i class="fas fa-heart"></i> 婚姻家庭类文书</li>
                        <li><i class="fas fa-briefcase"></i> 劳动争议类文书</li>
                        <li><i class="fas fa-download"></i> 一键生成和下载</li>
                    </ul>
                </div>
            </div>

            <h3>语音交互技术架构</h3>
            <div class="code-block">
def chat_with_voice(self, audio_data, voice="longxiaochun_v2"):
    """完整语音对话流程"""
    # 1. 语音识别 -> 文本
    text_input = self.speech_to_text(audio_data)

    # 2. 文本处理 -> AI回复
    text_response = self.chat_with_history(text_input)

    # 3. 语音合成 -> 音频输出
    audio_response = self.text_to_speech(text_response, voice)

    return {
        "text_input": text_input,
        "text_response": text_response,
        "audio_response": audio_response
    }
            </div>
        </section>

        <section id="technology" class="section">
            <h2><i class="fas fa-code"></i> 核心技术</h2>

            <h3>会话流程</h3>
            <div class="architecture-diagram">
                <div style="margin: 20px 0;">
                    <div class="flow-step">用户输入</div>
                    <div class="flow-step">智能体处理</div>
                    <div class="flow-step">大模型推理</div>
                    <div class="flow-step">结果生成</div>
                    <div class="flow-step">用户反馈</div>
                </div>
            </div>

            <h3>智能体架构</h3>
            <div class="grid">
                <div class="card">
                    <h4><i class="fas fa-robot"></i> AgentBase 基类</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-cog"></i> 统一的智能体接口</li>
                        <li><i class="fas fa-memory"></i> 会话历史管理</li>
                        <li><i class="fas fa-microphone"></i> 语音功能集成</li>
                        <li><i class="fas fa-file-text"></i> 提示词管理</li>
                    </ul>
                </div>

                <div class="card">
                    <h4><i class="fas fa-layer-group"></i> 专门化智能体</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-balance-scale"></i> ScenarioAgent - 场景咨询</li>
                        <li><i class="fas fa-comments"></i> ConversationAgent - 通用对话</li>
                        <li><i class="fas fa-book"></i> VocabAgent - 法律学习</li>
                        <li><i class="fas fa-search"></i> CaseSearchAgent - 案例检索</li>
                    </ul>
                </div>
            </div>

            <h3>核心算法</h3>
            <div class="code-block">
# 语义检索算法
def semantic_search(query, case_database):
    # 1. 查询向量化
    query_vector = vectorize_text(query)

    # 2. 计算相似度
    similarities = []
    for case in case_database:
        similarity = cosine_similarity(query_vector, case['vector'])
        similarities.append((case, similarity))

    # 3. 排序返回Top-K结果
    return sorted(similarities, key=lambda x: x[1], reverse=True)[:10]

# 律师推荐算法
def recommend_lawyers(user_need, lawyer_database):
    for lawyer in lawyer_database:
        # 多因子评分
        specialty_score = calculate_specialty_match(user_need, lawyer)
        experience_score = calculate_experience_score(lawyer)
        reputation_score = calculate_reputation_score(lawyer)

        # 综合评分
        total_score = (specialty_score * 0.4 +
                      experience_score * 0.3 +
                      reputation_score * 0.3)

    return sorted_lawyers_by_score[:5]
            </div>

            <h3>项目特色与创新</h3>
            <div class="highlight">
                <h4><i class="fas fa-lightbulb"></i> 技术创新点</h4>
                <ul class="feature-list">
                    <li><i class="fas fa-users-cog"></i> 多智能体协同架构</li>
                    <li><i class="fas fa-bullseye"></i> 场景化深度定制</li>
                    <li><i class="fas fa-microphone-alt"></i> 多模态交互支持</li>
                    <li><i class="fas fa-brain"></i> 智能推荐算法</li>
                </ul>
            </div>

            <div class="highlight">
                <h4><i class="fas fa-heart"></i> 社会价值</h4>
                <ul class="feature-list">
                    <li><i class="fas fa-balance-scale"></i> 普惠法律服务，降低咨询门槛</li>
                    <li><i class="fas fa-graduation-cap"></i> 提高公众法律意识和素养</li>
                    <li><i class="fas fa-clock"></i> 提升司法服务效率</li>
                    <li><i class="fas fa-digital-tachograph"></i> 推动法律服务数字化转型</li>
                </ul>
            </div>
        </section>
    </div>

    <script>
        // 平滑滚动
        document.querySelectorAll('.nav-menu a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(20px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });
    </script>
</body>
</html>
