<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>律师推荐模块 - 流程图展示</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-tab {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            font-weight: 500;
        }

        .nav-tab:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .nav-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            display: none;
        }

        .section.active {
            display: block;
            animation: fadeInUp 0.6s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section h2 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section h3 {
            color: #764ba2;
            font-size: 1.8rem;
            margin: 30px 0 20px 0;
            border-left: 4px solid #667eea;
            padding-left: 20px;
        }

        .mermaid-container {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #e3f2fd;
            overflow-x: auto;
        }

        .mermaid {
            text-align: center;
            min-height: 400px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }

        .feature-card h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .algorithm-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .highlight-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-item {
            text-align: center;
            background: #667eea;
            color: white;
            padding: 25px;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: scale(1.05);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
        }

        .process-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin: 30px 0;
        }

        .process-step {
            background: #667eea;
            color: white;
            padding: 15px 25px;
            border-radius: 30px;
            position: relative;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .process-step:hover {
            background: #764ba2;
            transform: scale(1.05);
        }

        .process-step::after {
            content: '→';
            position: absolute;
            right: -20px;
            color: #667eea;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .process-step:last-child::after {
            display: none;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 25px;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-tabs {
                flex-direction: column;
                align-items: center;
            }
            
            .process-flow {
                flex-direction: column;
            }
            
            .process-step::after {
                content: '↓';
                right: auto;
                bottom: -15px;
            }
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #667eea;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-user-tie"></i> 律师推荐模块</h1>
            <p>智能律师匹配系统 - 流程图详解</p>
            
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showSection('overview')">
                    <i class="fas fa-home"></i> 系统概览
                </button>
                <button class="nav-tab" onclick="showSection('architecture')">
                    <i class="fas fa-sitemap"></i> 整体架构
                </button>
                <button class="nav-tab" onclick="showSection('algorithm')">
                    <i class="fas fa-brain"></i> 推荐算法
                </button>
                <button class="nav-tab" onclick="showSection('sequence')">
                    <i class="fas fa-clock"></i> 时序流程
                </button>
                <button class="nav-tab" onclick="showSection('technical')">
                    <i class="fas fa-code"></i> 技术实现
                </button>
            </div>
        </div>

        <!-- 系统概览 -->
        <section id="overview" class="section active">
            <h2><i class="fas fa-info-circle"></i> 律师推荐系统概览</h2>
            
            <div class="highlight-box">
                <h3>系统简介</h3>
                <p>律师推荐模块是LCA法律咨询助手的核心功能之一，基于多因子智能推荐算法，为用户匹配最适合的法律专家。系统通过分析用户的法律需求，结合律师的专业能力、经验、口碑等多维度信息，提供精准的律师推荐服务。</p>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">7</span>
                    <span>核心模块</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <span>评分维度</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">多源</span>
                    <span>数据整合</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">智能</span>
                    <span>匹配算法</span>
                </div>
            </div>

            <h3>核心流程</h3>
            <div class="process-flow">
                <div class="process-step">需求分析</div>
                <div class="process-step">数据获取</div>
                <div class="process-step">智能匹配</div>
                <div class="process-step">评分排序</div>
                <div class="process-step">结果推荐</div>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-brain"></i> 智能需求分析</h4>
                    <p>通过自然语言处理技术，深度理解用户的法律需求，识别案件类型、紧急程度、地理位置等关键信息。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-database"></i> 多源数据整合</h4>
                    <p>整合律师协会、法律服务平台、律师事务所等多个数据源，确保律师信息的全面性和准确性。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-calculator"></i> 多因子评分</h4>
                    <p>基于专业匹配、经验评分、口碑评价、地理位置、费用评估等多个维度进行综合评分。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-rocket"></i> 实时推荐</h4>
                    <p>采用高效的推荐算法和缓存机制，实现毫秒级的律师推荐响应，提供流畅的用户体验。</p>
                </div>
            </div>
        </section>

        <!-- 整体架构 -->
        <section id="architecture" class="section">
            <h2><i class="fas fa-sitemap"></i> 系统整体架构</h2>
            
            <div class="mermaid-container">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在加载架构图...</p>
                </div>
                <div class="mermaid">
graph TB
    subgraph "用户交互层"
        U1[用户描述法律需求]
        U2[选择专业领域]
        U3[设置筛选条件]
        U4[查看推荐结果]
        U5[联系律师]
    end
    
    subgraph "律师推荐智能体"
        LRA1[接收用户需求]
        LRA2[需求分析处理]
        LRA3[调用推荐算法]
        LRA4[结果排序优化]
        LRA5[格式化输出]
    end
    
    subgraph "需求分析模块"
        RA1[文本预处理]
        RA2[关键词提取]
        RA3[专业领域识别]
        RA4[紧急程度评估]
        RA5[案件复杂度分析]
    end
    
    subgraph "律师数据获取"
        LDA1[LawyerDataAPI数据接口]
        LDA2[Web搜索引擎]
        LDA3[律师网站爬取]
        LDA4[法律目录检索]
        LDA5[数据去重处理]
        LDA6[数据质量验证]
    end
    
    subgraph "推荐算法引擎"
        RE1[专业匹配算法]
        RE2[经验评分算法]
        RE3[口碑评价算法]
        RE4[地理位置算法]
        RE5[费用评估算法]
        RE6[综合评分计算]
    end
    
    subgraph "数据存储层"
        DS1[律师基础信息]
        DS2[专业领域标签]
        DS3[案例成功率]
        DS4[用户评价数据]
        DS5[地理位置信息]
        DS6[收费标准数据]
    end
    
    subgraph "外部数据源"
        EDS1[律师协会网站]
        EDS2[法律服务平台]
        EDS3[律师事务所官网]
        EDS4[法院判决书网站]
        EDS5[律师评价网站]
    end
    
    %% 主流程连接
    U1 --> LRA1
    U2 --> LRA1
    U3 --> LRA1
    LRA1 --> LRA2
    LRA2 --> RA1
    
    %% 需求分析流程
    RA1 --> RA2
    RA2 --> RA3
    RA3 --> RA4
    RA4 --> RA5
    RA5 --> LRA3
    
    %% 数据获取流程
    LRA3 --> LDA1
    LDA1 --> LDA2
    LDA2 --> LDA3
    LDA3 --> LDA4
    LDA4 --> LDA5
    LDA5 --> LDA6
    
    %% 推荐算法流程
    LDA6 --> RE1
    RA3 --> RE1
    RE1 --> RE2
    RE2 --> RE3
    RE3 --> RE4
    RE4 --> RE5
    RE5 --> RE6
    RE6 --> LRA4
    
    %% 数据存储连接
    LDA6 --> DS1
    DS1 --> DS2
    DS2 --> DS3
    DS3 --> DS4
    DS4 --> DS5
    DS5 --> DS6
    
    %% 外部数据源连接
    EDS1 --> LDA2
    EDS2 --> LDA3
    EDS3 --> LDA3
    EDS4 --> LDA4
    EDS5 --> LDA4
    
    %% 结果输出
    LRA4 --> LRA5
    LRA5 --> U4
    U4 --> U5
    
    %% 样式定义
    classDef userClass fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef agentClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef analysisClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef algorithmClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef storageClass fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef externalClass fill:#fafafa,stroke:#616161,stroke-width:2px
    
    class U1,U2,U3,U4,U5 userClass
    class LRA1,LRA2,LRA3,LRA4,LRA5 agentClass
    class RA1,RA2,RA3,RA4,RA5 analysisClass
    class LDA1,LDA2,LDA3,LDA4,LDA5,LDA6 dataClass
    class RE1,RE2,RE3,RE4,RE5,RE6 algorithmClass
    class DS1,DS2,DS3,DS4,DS5,DS6 storageClass
    class EDS1,EDS2,EDS3,EDS4,EDS5 externalClass
                </div>
            </div>

            <h3>架构特点</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-layer-group"></i> 分层架构</h4>
                    <p>采用7层分层架构设计，从用户交互到数据存储，每层职责清晰，便于维护和扩展。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-puzzle-piece"></i> 模块化设计</h4>
                    <p>各功能模块独立设计，松耦合高内聚，支持独立开发、测试和部署。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-sync-alt"></i> 数据流转</h4>
                    <p>清晰的数据流转路径，从需求分析到结果输出，确保数据处理的准确性和效率。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-shield-alt"></i> 容错机制</h4>
                    <p>多重容错和异常处理机制，确保系统在各种情况下的稳定运行。</p>
                </div>
            </div>
        </section>

        <!-- 推荐算法 -->
        <section id="algorithm" class="section">
            <h2><i class="fas fa-brain"></i> 推荐算法详解</h2>

            <div class="mermaid-container">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在加载算法流程图...</p>
                </div>
                <div class="mermaid">
flowchart TD
    Start([开始律师推荐]) --> Input[接收用户需求描述]

    Input --> Parse[需求文本解析]
    Parse --> Extract[提取关键信息]

    subgraph "需求分析"
        Extract --> Field[识别专业领域]
        Field --> Urgency[评估紧急程度]
        Urgency --> Complexity[分析案件复杂度]
        Complexity --> Location[确定地理位置]
        Location --> Budget[预算范围评估]
    end

    Budget --> GetLawyers[获取律师数据]

    subgraph "数据获取"
        GetLawyers --> WebSearch[Web搜索律师信息]
        WebSearch --> ScrapeData[爬取律师网站数据]
        ScrapeData --> ValidateData[数据验证和清洗]
        ValidateData --> DeduplicateData[去重处理]
    end

    DeduplicateData --> StartScoring[开始评分计算]

    subgraph "评分算法"
        StartScoring --> SpecialtyScore[专业匹配评分]
        SpecialtyScore --> ExperienceScore[经验评分]
        ExperienceScore --> ReputationScore[口碑评分]
        ReputationScore --> LocationScore[地理位置评分]
        LocationScore --> CostScore[费用评分]
        CostScore --> WeightedSum[加权综合评分]
    end

    WeightedSum --> Sort[按评分排序]
    Sort --> Filter[应用筛选条件]
    Filter --> TopN[选择Top-N律师]

    subgraph "结果优化"
        TopN --> Diversify[结果多样化处理]
        Diversify --> AddDetails[补充详细信息]
        AddDetails --> FormatOutput[格式化输出]
    end

    FormatOutput --> Return[返回推荐结果]
    Return --> End([结束])

    %% 决策节点
    GetLawyers --> HasCache{是否有缓存数据?}
    HasCache -->|是| UseCache[使用缓存数据]
    HasCache -->|否| WebSearch
    UseCache --> StartScoring

    Filter --> EnoughResults{结果数量足够?}
    EnoughResults -->|是| TopN
    EnoughResults -->|否| ExpandSearch[扩大搜索范围]
    ExpandSearch --> GetLawyers

    %% 样式定义
    classDef processClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef algorithmClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef startEndClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px

    class Start,End startEndClass
    class HasCache,EnoughResults decisionClass
    class GetLawyers,WebSearch,ScrapeData,ValidateData,DeduplicateData,UseCache,ExpandSearch dataClass
    class SpecialtyScore,ExperienceScore,ReputationScore,LocationScore,CostScore,WeightedSum algorithmClass
                </div>
            </div>

            <h3>多因子评分算法</h3>
            <div class="algorithm-box">
def recommend_lawyers(user_requirement, filters=None):
    """律师推荐主算法"""
    # 1. 需求分析
    analyzed_requirement = analyze_requirement(user_requirement)

    # 2. 获取律师数据
    lawyers = get_lawyer_data(analyzed_requirement.field)

    # 3. 计算评分
    scored_lawyers = []
    for lawyer in lawyers:
        # 专业匹配评分 (权重: 40%)
        specialty_score = calculate_specialty_match(
            analyzed_requirement.field, lawyer.specialties
        )

        # 经验评分 (权重: 30%)
        experience_score = calculate_experience_score(
            lawyer.years_of_practice, lawyer.case_count, lawyer.success_rate
        )

        # 口碑评分 (权重: 20%)
        reputation_score = calculate_reputation_score(
            lawyer.rating, lawyer.review_count, lawyer.client_feedback
        )

        # 地理位置评分 (权重: 10%)
        location_score = calculate_location_score(
            analyzed_requirement.location, lawyer.location
        )

        # 综合评分
        total_score = (
            specialty_score * 0.4 + experience_score * 0.3 +
            reputation_score * 0.2 + location_score * 0.1
        )

        scored_lawyers.append((lawyer, total_score))

    # 4. 排序和筛选
    scored_lawyers.sort(key=lambda x: x[1], reverse=True)
    return [lawyer for lawyer, score in scored_lawyers[:10]]
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-bullseye"></i> 专业匹配 (40%)</h4>
                    <p>分析律师专业领域与用户需求的匹配度，包括直接匹配和相关专业匹配。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-medal"></i> 经验评分 (30%)</h4>
                    <p>综合考虑律师执业年限、处理案例数量和成功率等经验指标。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-star"></i> 口碑评价 (20%)</h4>
                    <p>基于客户评价、同行评价和媒体报道等多维度口碑信息。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-map-marker-alt"></i> 地理位置 (10%)</h4>
                    <p>考虑律师与用户的地理距离，优先推荐本地或邻近地区的律师。</p>
                </div>
            </div>
        </section>

        <!-- 时序流程 -->
        <section id="sequence" class="section">
            <h2><i class="fas fa-clock"></i> 系统时序流程</h2>

            <div class="mermaid-container">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在加载时序图...</p>
                </div>
                <div class="mermaid">
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端界面
    participant API as Flask API
    participant LRAgent as 律师推荐智能体
    participant DataAPI as 律师数据API
    participant WebSearch as Web搜索引擎
    participant Algorithm as 推荐算法
    participant Database as 数据库

    User->>Frontend: 1. 输入法律需求
    Frontend->>API: 2. 发送推荐请求
    API->>LRAgent: 3. 调用律师推荐智能体

    LRAgent->>LRAgent: 4. 解析用户需求
    LRAgent->>Database: 5. 检查缓存数据

    alt 缓存未命中
        LRAgent->>DataAPI: 6. 请求律师数据
        DataAPI->>WebSearch: 7. 搜索律师信息
        WebSearch-->>DataAPI: 8. 返回搜索结果
        DataAPI->>DataAPI: 9. 数据清洗和验证
        DataAPI-->>LRAgent: 10. 返回律师数据
        LRAgent->>Database: 11. 缓存律师数据
    else 缓存命中
        Database-->>LRAgent: 6. 返回缓存数据
    end

    LRAgent->>Algorithm: 12. 调用推荐算法
    Algorithm->>Algorithm: 13. 计算专业匹配度
    Algorithm->>Algorithm: 14. 计算经验评分
    Algorithm->>Algorithm: 15. 计算口碑评分
    Algorithm->>Algorithm: 16. 综合评分排序
    Algorithm-->>LRAgent: 17. 返回推荐结果

    LRAgent->>LRAgent: 18. 格式化输出结果
    LRAgent-->>API: 19. 返回推荐律师列表
    API-->>Frontend: 20. 返回JSON响应
    Frontend-->>User: 21. 展示推荐律师

    User->>Frontend: 22. 选择律师查看详情
    Frontend->>API: 23. 请求律师详细信息
    API->>Database: 24. 查询律师详情
    Database-->>API: 25. 返回详细信息
    API-->>Frontend: 26. 返回律师详情
    Frontend-->>User: 27. 展示律师详细信息

    Note over User,Database: 完整的律师推荐流程
                </div>
            </div>

            <h3>关键时序节点</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-play"></i> 请求接收</h4>
                    <p>用户输入法律需求，前端验证并发送到后端API，启动推荐流程。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-search"></i> 数据获取</h4>
                    <p>智能体检查缓存，如未命中则调用数据API从多个源获取律师信息。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-calculator"></i> 算法计算</h4>
                    <p>推荐算法并行计算各维度评分，进行加权综合评分和排序。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-list"></i> 结果返回</h4>
                    <p>格式化推荐结果，返回给前端展示，支持详情查看和联系功能。</p>
                </div>
            </div>
        </section>

        <!-- 技术实现 -->
        <section id="technical" class="section">
            <h2><i class="fas fa-code"></i> 技术实现详解</h2>

            <h3>核心技术栈</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fab fa-python"></i> Python后端</h4>
                    <p>使用Python作为主要开发语言，Flask框架提供RESTful API服务。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-robot"></i> 智能体架构</h4>
                    <p>基于AgentBase抽象类，实现LawyerRecommendationAgent专门智能体。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-spider"></i> 数据爬取</h4>
                    <p>使用WebSearchEngine和多线程技术，实时爬取律师信息数据。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-database"></i> 数据存储</h4>
                    <p>内存缓存结合持久化存储，确保数据访问效率和可靠性。</p>
                </div>
            </div>

            <h3>专业匹配算法实现</h3>
            <div class="algorithm-box">
def calculate_specialty_match(required_field, lawyer_specialties):
    """计算专业匹配度"""
    # 直接匹配
    if required_field in lawyer_specialties:
        return 1.0

    # 相关专业匹配映射
    field_similarity_map = {
        '婚姻纠纷': ['家庭法', '民事诉讼', '财产分割'],
        '合同纠纷': ['商事诉讼', '民事诉讼', '经济法'],
        '工伤赔偿': ['劳动法', '人身损害', '社会保险']
    }

    related_fields = field_similarity_map.get(required_field, [])
    max_similarity = 0.0

    for specialty in lawyer_specialties:
        if specialty in related_fields:
            max_similarity = max(max_similarity, 0.8)
        else:
            # 使用文本相似度计算
            similarity = calculate_text_similarity(required_field, specialty)
            max_similarity = max(max_similarity, similarity * 0.6)

    return max_similarity
            </div>

            <h3>系统优化策略</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-tachometer-alt"></i> 性能优化</h4>
                    <p>多级缓存机制、异步处理、连接池等技术，确保系统高性能运行。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-shield-alt"></i> 容错处理</h4>
                    <p>完善的异常处理机制，数据验证和清洗，确保系统稳定可靠。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-expand-arrows-alt"></i> 可扩展性</h4>
                    <p>模块化设计，支持新数据源接入、新评分算法添加等扩展需求。</p>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-chart-line"></i> 监控分析</h4>
                    <p>完整的日志记录、性能监控和数据分析，支持系统持续优化。</p>
                </div>
            </div>

            <div class="highlight-box">
                <h3><i class="fas fa-lightbulb"></i> 创新亮点</h3>
                <ul style="list-style: none; padding: 0;">
                    <li style="padding: 8px 0;"><i class="fas fa-check" style="color: #667eea; margin-right: 10px;"></i> 多因子智能评分算法，提供精准推荐</li>
                    <li style="padding: 8px 0;"><i class="fas fa-check" style="color: #667eea; margin-right: 10px;"></i> 实时数据获取和更新，确保信息时效性</li>
                    <li style="padding: 8px 0;"><i class="fas fa-check" style="color: #667eea; margin-right: 10px;"></i> 自然语言需求分析，提升用户体验</li>
                    <li style="padding: 8px 0;"><i class="fas fa-check" style="color: #667eea; margin-right: 10px;"></i> 多源数据整合，提供全面律师信息</li>
                    <li style="padding: 8px 0;"><i class="fas fa-check" style="color: #667eea; margin-right: 10px;"></i> 智能缓存和优化，实现毫秒级响应</li>
                </ul>
            </div>
        </section>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });

        // 标签页切换功能
        function showSection(sectionId) {
            // 隐藏所有section
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 移除所有tab的active状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示目标section
            document.getElementById(sectionId).classList.add('active');
            
            // 激活对应的tab
            event.target.classList.add('active');
            
            // 重新渲染Mermaid图表
            setTimeout(() => {
                mermaid.init();
            }, 100);
        }

        // 页面加载完成后隐藏loading
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.querySelectorAll('.loading').forEach(loading => {
                    loading.style.display = 'none';
                });
            }, 1000);
        });

        // 平滑滚动效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
