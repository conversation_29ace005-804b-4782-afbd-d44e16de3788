import json
from abc import ABC, abstractmethod
import hashlib
import time
import io
import base64
import tempfile

# from langchain_ollama.chat_models import ChatOllama  # 导入 ChatOllama 模型 (已禁用)
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder  # 导入提示模板相关类
from langchain_core.messages import HumanMessage  # 导入消息类
from langchain_core.runnables.history import RunnableWithMessageHistory  # 导入带有消息历史的可运行类

from .session_history import get_session_history  # 导入会话历史相关方法
from utils.logger import LOG  # 导入日志工具
from langchain_community.chat_models import ChatTongyi  # 导入千问模型
import os

# 导入subprocess用于ffmpeg调用
import subprocess

# 导入阿里云通义千问语音相关模块
try:
    import dashscope
    # 使用Paraformer实时语音识别模型
    from dashscope.audio.asr import Recognition, RecognitionCallback, RecognitionResult
    from dashscope.audio.tts_v2 import SpeechSynthesizer, ResultCallback, AudioFormat
    SPEECH_AVAILABLE = True
except ImportError:
    LOG.warning("DashScope SDK not available. Speech features will be disabled.")
    SPEECH_AVAILABLE = False

# 简单的响应缓存
response_cache = {}
CACHE_EXPIRY = 300  # 5分钟缓存过期

class AgentBase(ABC):
    """
    抽象基类，提供代理的共有功能。
    设计模式: 抽象基类模式
    核心功能:
            加载系统提示词和初始消息
            初始化千问大模型 (ChatTongyi)
            管理对话历史记录
            提供统一的对话接口
            支持语音识别和语音合成功能

    """
    def __init__(self, name, prompt_file, intro_file=None, session_id=None):
        self.name = name
        self.prompt_file = prompt_file
        self.intro_file = intro_file
        self.session_id = session_id if session_id else self.name
        self.prompt = self.load_prompt()
        self.intro_messages = self.load_intro() if self.intro_file else []
        self.create_chatbot()

        # 初始化语音功能
        self.init_speech_services()

    def load_prompt(self):
        """
        从文件加载系统提示语。
        """
        try:
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(self.prompt_file):
                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                prompt_file_path = os.path.join(project_root, self.prompt_file)
            else:
                prompt_file_path = self.prompt_file

            with open(prompt_file_path, "r", encoding="utf-8") as file:
                return file.read().strip()
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到提示文件 {self.prompt_file}!")

    def load_intro(self):
        """
        从 JSON 文件加载初始消息。
        """
        try:
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(self.intro_file):
                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                intro_file_path = os.path.join(project_root, self.intro_file)
            else:
                intro_file_path = self.intro_file

            with open(intro_file_path, "r", encoding="utf-8") as file:
                return json.load(file)
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到初始消息文件 {self.intro_file}!")
        except json.JSONDecodeError:
            raise ValueError(f"初始消息文件 {self.intro_file} 包含无效的 JSON!")

    def create_chatbot(self):
        """
        初始化聊天机器人，包括系统提示和消息历史记录。
        """
        # 创建聊天提示模板，包括系统提示和消息占位符
        system_prompt = ChatPromptTemplate.from_messages([
            ("system", self.prompt),  # 系统提示部分
            MessagesPlaceholder(variable_name="messages"),  # 消息占位符
        ])

        
        # 初始化 ChatOllama 模型，配置参数
        # self.chatbot = system_prompt | ChatOllama(
        #     model="llama3.1:8b-instruct-q8_0",  # 使用的模型名称
        #     max_tokens=8192,  # 最大生成的 token 数
        #     temperature=0.8,  # 随机性配置
        # )

        # 初始化千问模型，配置参数
        from dotenv import load_dotenv, find_dotenv
        _ = load_dotenv(find_dotenv())  # 读取本地 .env 文件，里面定义了 Qwen_API_KEY
        self.chatbot = system_prompt | ChatTongyi(
            model="qwen-turbo",  # 使用千问turbo模型
            dashscope_api_key=os.getenv("Qwen_API_KEY"),  # 从环境变量获取千问API密钥
            max_tokens=2048,  # 减少最大token数以提高响应速度
            temperature=0.7,  # 降低随机性以提高一致性
            streaming=True,  # 启用流式输出以提高响应速度
            top_p=0.8,  # 添加top_p参数优化生成质量
            timeout=30  # 设置超时时间
        )

        # 将聊天机器人与消息历史记录关联
        self.chatbot_with_history = RunnableWithMessageHistory(self.chatbot, get_session_history)

    def chat_with_history(self, user_input, session_id=None):
        """
        处理用户输入，生成包含聊天历史的回复。

        参数:
            user_input (str): 用户输入的消息
            session_id (str, optional): 会话的唯一标识符

        返回:
            str: AI 生成的回复
        """
        if session_id is None:
            session_id = self.session_id

        # 生成缓存键
        cache_key = hashlib.md5(f"{self.name}_{session_id}_{user_input}".encode()).hexdigest()
        current_time = time.time()

        # 检查缓存
        if cache_key in response_cache:
            cached_response, timestamp = response_cache[cache_key]
            if current_time - timestamp < CACHE_EXPIRY:
                LOG.debug(f"[ChatBot][{self.name}] Using cached response")
                return cached_response

        response = self.chatbot_with_history.invoke(
            [HumanMessage(content=user_input)],  # 将用户输入封装为 HumanMessage
            {"configurable": {"session_id": session_id}},  # 传入配置，包括会话ID
        )

        # 缓存响应
        response_cache[cache_key] = (response.content, current_time)

        # 清理过期缓存
        self._cleanup_cache()

        LOG.debug(f"[ChatBot][{self.name}] {response.content}")  # 记录调试日志

        # 格式化输出以确保一致性
        #formatted_response = self.format_response(response.content)
        return response.content

    def _cleanup_cache(self):
        """清理过期的缓存条目"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in response_cache.items()
            if current_time - timestamp >= CACHE_EXPIRY
        ]
        for key in expired_keys:
            del response_cache[key]

    def format_response(self, response_content):
        """
        格式化AI回复内容，确保所有智能体输出格式一致

        参数:
            response_content (str): AI原始回复内容

        返回:
            str: 格式化后的回复内容
        """
        # 如果回复已经包含<br>标签，直接返回
        if '<br>' in response_content:
            return response_content

        # 将换行符转换为<br>标签，确保格式一致
        formatted_content = response_content.replace('\n\n', '<br><br>')
        formatted_content = formatted_content.replace('\n', '<br>')

        return formatted_content

    def init_speech_services(self):
        """
        初始化语音服务，包括语音识别和语音合成
        """
        if not SPEECH_AVAILABLE:
            LOG.warning("Speech services not available - DashScope SDK not installed")
            return

        try:
            # 从环境变量加载API Key
            from dotenv import load_dotenv, find_dotenv
            _ = load_dotenv(find_dotenv())

            # 设置DashScope API Key
            dashscope.api_key = os.getenv("Qwen_API_KEY")

            if not dashscope.api_key:
                LOG.error("Qwen_API_KEY not found in environment variables")
                return

            LOG.info(f"[{self.name}] Speech services initialized successfully")

        except Exception as e:
            LOG.error(f"[{self.name}] Failed to initialize speech services: {str(e)}")

    def speech_to_text(self, audio_data, audio_format="wav", sample_rate=16000):
        """
        语音识别：将音频数据转换为文本
        使用Paraformer实时语音识别模型，支持多种格式

        参数:
            audio_data (bytes): 音频数据
            audio_format (str): 音频格式，支持 wav, mp3, pcm, webm 等
            sample_rate (int): 采样率，默认16000Hz

        返回:
            str: 识别出的文本，失败时返回None
        """
        if not SPEECH_AVAILABLE:
            LOG.error("Speech recognition not available - DashScope SDK not installed")
            return None

        # 验证音频数据
        if not audio_data or len(audio_data) == 0:
            LOG.error("Audio data is empty or None")
            return None

        if len(audio_data) < 100:  # 音频数据太小，可能无效
            LOG.error(f"Audio data too small: {len(audio_data)} bytes")
            return None

        # 增强的音频数据验证
        validation_result = self._validate_audio_data(audio_data, audio_format)
        if not validation_result["valid"]:
            LOG.error(f"Audio data validation failed: {validation_result['reason']}")
            return None



        # 存储当前音频数据供格式尝试使用
        self.current_audio_data = audio_data

        # 尝试多种格式和模型组合
        format_attempts = self._get_format_attempts(audio_format, sample_rate)

        for processed_data, processed_format, processed_sample_rate, model_name in format_attempts:
            try:
                result = self._try_speech_recognition(processed_data, processed_format, processed_sample_rate, model_name)
                if result:
                    return result
            except Exception:
                continue

        LOG.error("All speech recognition attempts failed")
        return None

    def _get_format_attempts(self, audio_format, sample_rate):
        """
        获取格式尝试列表，按优先级排序

        返回:
            list: [(audio_data, format, sample_rate, model), ...]
        """
        attempts = []

        # 分析音频数据
        self._analyze_audio_data(self.current_audio_data, audio_format)

        if audio_format.lower() in ['webm', 'webm/opus']:
            # WebM格式：优先使用ffmpeg转换，然后回退
            # 1. 使用ffmpeg转换WebM到WAV（更兼容的方法）
            wav_data = self._convert_webm_with_ffmpeg(self.current_audio_data)
            if wav_data:
                attempts.append((wav_data, "wav", 8000, "paraformer-realtime-8k-v2"))

            # 2. 简化的回退策略（仅在ffmpeg转换失败时使用）
            if not wav_data:
                attempts.extend([
                    # 尝试直接作为opus格式处理
                    (self.current_audio_data, "opus", 8000, "paraformer-realtime-8k-v2"),
                    # 尝试作为wav格式处理
                    (self.current_audio_data, "wav", 16000, "paraformer-realtime-v1"),
                ])
        else:
            # 其他格式的处理
            processed_data, processed_format, processed_sample_rate = self._process_audio_for_paraformer(
                self.current_audio_data, audio_format, sample_rate
            )
            model_name = self._select_paraformer_model(processed_sample_rate)
            attempts.append((processed_data, processed_format, processed_sample_rate, model_name))

        return attempts

    def _try_speech_recognition(self, audio_data, audio_format, sample_rate, model_name):
        """
        尝试使用指定参数进行语音识别
        """
        # 创建语音识别回调类
        class ParaformerCallback(RecognitionCallback):
            def __init__(self):
                self.result_text = ""
                self.is_complete = False
                self.error_message = None

            def on_open(self):
                pass

            def on_close(self):
                pass

            def on_event(self, result: RecognitionResult):
                try:
                    if result and hasattr(result, 'get_sentence') and result.get_sentence():
                        sentence = result.get_sentence()
                        LOG.debug(f"Received sentence: {sentence}")

                        # 处理不同的数据结构
                        if isinstance(sentence, dict):
                            # 如果sentence是字典，尝试获取text字段
                            text = sentence.get('text') or sentence.get('content') or sentence.get('result')
                            if text:
                                # 累积所有句子，而不是覆盖
                                if sentence.get('sentence_end', False):
                                    # 句子结束时，累积到结果中
                                    if self.result_text:
                                        self.result_text += ' ' + text
                                    else:
                                        self.result_text = text
                                    LOG.debug(f"Sentence ended, accumulated text: {self.result_text}")
                                else:
                                    # 句子未结束时，只更新当前句子（用于实时显示）
                                    LOG.debug(f"Partial sentence: {text}")

                                # 检查是否所有句子都结束了（这里简化处理，实际可能需要更复杂的逻辑）
                                if sentence.get('sentence_end', False):
                                    # 可以设置完成标志，但通常等待更多句子
                                    pass
                        elif hasattr(sentence, 'text') and sentence.text:
                            # 如果sentence是对象，使用text属性
                            if hasattr(sentence, 'sentence_end') and sentence.sentence_end:
                                # 句子结束时累积
                                if self.result_text:
                                    self.result_text += ' ' + sentence.text
                                else:
                                    self.result_text = sentence.text
                                LOG.debug(f"Object sentence ended, accumulated text: {self.result_text}")
                            else:
                                LOG.debug(f"Partial object sentence: {sentence.text}")
                        elif isinstance(sentence, str):
                            # 如果sentence直接是字符串
                            self.result_text = sentence
                            LOG.debug(f"Direct string sentence: {sentence}")
                        else:
                            LOG.debug(f"Unknown sentence format: {type(sentence)}, content: {sentence}")
                except Exception as e:
                    LOG.debug(f"Error processing recognition result: {e}")
                    # 不要因为处理错误而中断识别过程

            def on_error(self, message):
                self.error_message = str(message)
                self.is_complete = True

            def on_complete(self):
                self.is_complete = True

        # 创建回调实例
        callback = ParaformerCallback()

        # 创建语音识别器
        recognition = Recognition(
            model=model_name,
            format=audio_format,
            sample_rate=sample_rate,
            callback=callback
        )

        # 启动识别
        recognition.start()

        # 发送音频数据
        chunk_size = 3200
        sent_chunks = 0
        for i in range(0, len(audio_data), chunk_size):
            chunk = audio_data[i:i + chunk_size]
            recognition.send_audio_frame(chunk)
            sent_chunks += 1

        LOG.debug(f"Sent {sent_chunks} audio chunks to {model_name}")

        # 停止识别
        recognition.stop()

        # 等待识别完成
        import time
        max_wait_time = 10  # 最多等待10秒
        wait_interval = 0.1  # 每100ms检查一次
        waited_time = 0

        while not callback.is_complete and waited_time < max_wait_time:
            time.sleep(wait_interval)
            waited_time += wait_interval

        if not callback.is_complete:
            LOG.warning(f"Speech recognition timeout after {max_wait_time}s for {model_name}")
            return None

        # 检查结果
        if callback.error_message:
            # 如果是格式不支持错误或无效音频错误，抛出异常让上层尝试下一种格式
            error_msg = callback.error_message
            if any(error_type in error_msg for error_type in [
                "UNSUPPORTED_FORMAT",
                "NO_VALID_AUDIO_ERROR",
                "INVALID_AUDIO_FORMAT",
                "AUDIO_FORMAT_ERROR"
            ]):
                LOG.debug(f"Format-related error for {audio_format}: {error_msg}")
                raise ValueError(f"Format error for {audio_format}: {error_msg}")
            else:
                LOG.error(f"Speech recognition failed: {error_msg}")
                return None

        # 返回识别结果
        result_text = callback.result_text.strip() if callback.result_text else None
        LOG.debug(f"Final recognition result: '{result_text}'")
        return result_text if result_text else None

    def _select_paraformer_model(self, sample_rate):
        """
        根据采样率选择合适的Paraformer模型
        """
        if sample_rate <= 8000:
            return "paraformer-realtime-8k-v2"  # 支持8kHz，支持webm格式
        elif sample_rate == 16000:
            return "paraformer-realtime-v1"     # 支持16kHz
        else:
            return "paraformer-realtime-v2"     # 支持任意采样率

    def _process_audio_for_paraformer(self, audio_data, audio_format, sample_rate):
        """
        处理音频数据以适配Paraformer ASR API的格式要求

        参数:
            audio_data (bytes): 原始音频数据
            audio_format (str): 原始音频格式
            sample_rate (int): 原始采样率

        返回:
            tuple: (处理后的音频数据, 处理后的格式, 处理后的采样率)
        """
        # Paraformer支持的格式：pcm、wav、mp3、opus、speex、aac、amr
        # paraformer-realtime-8k-v2 还支持 webm 格式

        # 分析音频数据头部，帮助诊断问题
        self._analyze_audio_data(audio_data, audio_format)

        # 处理webm格式 - 虽然文档说支持webm，但实际需要使用opus格式
        if audio_format.lower() in ['webm', 'webm/opus']:
            LOG.debug(f"Converting webm to opus format for paraformer-realtime-8k-v2")
            # WebM容器通常包含Opus编码的音频，直接使用opus格式
            # 注意：这里我们假设webm容器中是opus编码，这是最常见的情况
            return audio_data, "opus", 8000

        # 处理其他格式
        format_mapping = {
            'wav': ('wav', sample_rate),
            'mp3': ('mp3', sample_rate),
            'pcm': ('pcm', sample_rate),
            'opus': ('opus', sample_rate),
            'speex': ('speex', sample_rate),
            'aac': ('aac', sample_rate),
            'amr': ('amr', sample_rate),
            'mp4': ('aac', sample_rate),  # MP4通常包含AAC
            'ogg': ('opus', sample_rate), # OGG通常包含opus
        }

        mapped_format, mapped_sample_rate = format_mapping.get(audio_format.lower(), ('wav', sample_rate))
        LOG.debug(f"Mapped {audio_format} to {mapped_format} with sample rate {mapped_sample_rate}")

        return audio_data, mapped_format, mapped_sample_rate

    def _process_audio_for_asr(self, audio_data, audio_format):
        """
        处理音频数据以适配ASR API的格式要求

        参数:
            audio_data (bytes): 原始音频数据
            audio_format (str): 原始音频格式

        返回:
            tuple: (处理后的音频数据, 处理后的格式)
        """
        # 通义千问ASR支持的格式：pcm、wav、mp3、opus、speex、aac、amr
        supported_formats = ['pcm', 'wav', 'mp3', 'opus', 'speex', 'aac', 'amr']

        # 分析音频数据头部，帮助诊断问题
        self._analyze_audio_data(audio_data, audio_format)

        # 如果格式已经支持，直接返回
        if audio_format.lower() in supported_formats:
            return audio_data, audio_format.lower()

        # 处理webm格式（通常包含opus编码）
        if audio_format.lower() in ['webm', 'webm/opus']:
            # WebM容器格式可能需要特殊处理
            LOG.warning(f"WebM format detected, this may cause compatibility issues")

            # 检查是否是有效的WebM文件
            if audio_data.startswith(b'\x1a\x45\xdf\xa3'):  # WebM/Matroska magic number
                LOG.debug("Valid WebM container detected")
                # 尝试直接使用webm格式
                return audio_data, "webm"
            else:
                LOG.warning("Invalid WebM container, trying as opus")
                return audio_data, "opus"

        # 处理mp4格式（通常包含aac编码）
        if audio_format.lower() in ['mp4', 'mp4/aac']:
            LOG.debug(f"Treating {audio_format} as aac format")
            return audio_data, "aac"

        # 处理其他常见格式映射
        format_mapping = {
            'ogg': 'opus',  # OGG通常包含opus
            'oga': 'opus',  # OGA是OGG音频
            'm4a': 'aac',   # M4A通常是AAC
            'flac': 'wav',  # FLAC尝试作为WAV处理
            'wma': 'wav',   # WMA尝试作为WAV处理
        }

        mapped_format = format_mapping.get(audio_format.lower())
        if mapped_format:
            LOG.debug(f"Mapped {audio_format} to {mapped_format}")
            return audio_data, mapped_format

        # 其他格式默认尝试wav
        LOG.warning(f"Unsupported audio format: {audio_format}, trying as WAV")
        return audio_data, "wav"

    def _validate_audio_data(self, audio_data, audio_format):
        """
        验证音频数据的有效性

        参数:
            audio_data (bytes): 音频数据
            audio_format (str): 音频格式

        返回:
            dict: {"valid": bool, "reason": str}
        """
        try:
            # 基本长度检查
            if len(audio_data) < 44:  # 最小WAV头部长度
                return {"valid": False, "reason": f"Audio data too short: {len(audio_data)} bytes"}

            # 检查是否全为零或重复数据（静音检测）
            if len(set(audio_data[:1000])) < 5:
                return {"valid": False, "reason": "Audio data appears to be silent or corrupted"}

            # 格式特定验证
            if audio_format.lower() in ['webm', 'webm/opus']:
                return self._validate_webm_data(audio_data)
            elif audio_format.lower() == 'wav':
                return self._validate_wav_data(audio_data)
            elif audio_format.lower() == 'mp3':
                return self._validate_mp3_data(audio_data)
            else:
                # 对于其他格式，进行基本验证
                return {"valid": True, "reason": "Basic validation passed"}

        except Exception as e:
            return {"valid": False, "reason": f"Validation error: {str(e)}"}

    def _validate_webm_data(self, webm_data):
        """
        验证WebM音频数据
        """
        # 检查WebM/Matroska魔数
        if not webm_data.startswith(b'\x1a\x45\xdf\xa3'):
            return {"valid": False, "reason": "Invalid WebM header"}

        # 检查EBML头部结构的基本完整性
        if len(webm_data) < 100:  # WebM头部通常需要更多字节
            return {"valid": False, "reason": "WebM data too short for valid container"}

        # 简单检查是否包含音频相关的EBML元素
        # 这是一个简化的检查，真正的WebM解析需要完整的EBML解析器
        audio_indicators = [
            b'\x16\x54\xae\x6b',  # Tracks element
            b'\xae',              # TrackEntry
            b'\x83',              # TrackType
        ]

        has_audio_indicators = any(indicator in webm_data[:2000] for indicator in audio_indicators)
        if not has_audio_indicators:
            LOG.warning("WebM container may not contain audio tracks")

        return {"valid": True, "reason": "WebM validation passed"}

    def _validate_wav_data(self, wav_data):
        """
        验证WAV音频数据
        """
        if not wav_data.startswith(b'RIFF'):
            return {"valid": False, "reason": "Invalid WAV header"}

        if len(wav_data) < 44:
            return {"valid": False, "reason": "WAV data too short"}

        # 检查WAV格式标识
        if wav_data[8:12] != b'WAVE':
            return {"valid": False, "reason": "Invalid WAV format identifier"}

        return {"valid": True, "reason": "WAV validation passed"}

    def _validate_mp3_data(self, mp3_data):
        """
        验证MP3音频数据
        """
        # 检查MP3帧头
        if not (mp3_data.startswith(b'\xff\xfb') or mp3_data.startswith(b'\xff\xf3') or
                mp3_data.startswith(b'\xff\xf2') or mp3_data.startswith(b'\xff\xfa')):
            return {"valid": False, "reason": "Invalid MP3 header"}

        return {"valid": True, "reason": "MP3 validation passed"}

    def _find_ffmpeg(self):
        """
        查找ffmpeg可执行文件

        返回:
            str: ffmpeg路径，找不到时返回None
        """
        # 已知的ffmpeg位置
        path = r'C:\Users\<USER>\Desktop\LegalConsultationAssistant-v1\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe'
        
        try:
            result = subprocess.run([path, '-version'],
                                      capture_output=True, timeout=5)
            if result.returncode == 0:
                LOG.debug(f"Found ffmpeg at: {path}")
                return path
        except (FileNotFoundError, subprocess.TimeoutExpired):
            return None
        

    def _convert_webm_with_ffmpeg(self, webm_data):
        """
        使用ffmpeg将WebM音频数据转换为Opus格式

        参数:
            webm_data (bytes): WebM音频数据

        返回:
            bytes: 转换后的Opus音频数据，失败时返回None
        """
        # 查找ffmpeg
        ffmpeg_path = self._find_ffmpeg()
        if not ffmpeg_path:
            LOG.error("ffmpeg not found. Please install ffmpeg and ensure it's in PATH.")
            return None

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_webm:
                temp_webm.write(webm_data)
                temp_webm_path = temp_webm.name

            with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
                temp_opus_path = temp_opus.name

            try:
                # 使用ffmpeg转换WebM到WAV格式（更兼容）
                # 先尝试转换为WAV格式，因为可能Opus格式有兼容性问题
                temp_wav_path = temp_opus_path.replace('.opus', '.wav')
                cmd = [
                    ffmpeg_path,
                    '-i', temp_webm_path,      # 输入WebM文件
                    '-vn',                     # 不处理视频
                    '-acodec', 'pcm_s16le',    # 使用PCM编码器
                    '-ar', '8000',             # 采样率8kHz
                    '-ac', '1',                # 单声道
                    '-y',                      # 覆盖输出文件
                    temp_wav_path
                ]

                # 执行ffmpeg命令
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30  # 30秒超时
                )

                if result.returncode == 0:
                    # 读取转换后的WAV数据
                    with open(temp_wav_path, 'rb') as f:
                        wav_data = f.read()


                    return wav_data
                else:
                    LOG.warning(f"ffmpeg conversion failed: {result.stderr}")
                    return None

            finally:
                # 清理临时文件
                for temp_path in [temp_webm_path, temp_opus_path, temp_wav_path]:
                    if os.path.exists(temp_path):
                        try:
                            os.unlink(temp_path)
                        except Exception as e:
                            LOG.debug(f"Failed to cleanup temp file {temp_path}: {e}")

        except subprocess.TimeoutExpired:
            LOG.error("ffmpeg conversion timeout")
            return None
        except Exception as e:
            LOG.warning(f"ffmpeg WebM conversion failed: {str(e)}")
            return None

    def _analyze_audio_data(self, audio_data, audio_format):
        """
        分析音频数据，帮助诊断问题
        """
        if len(audio_data) < 16:
            return

        # 获取前16字节用于分析
        header = audio_data[:16]
        header_hex = header.hex()

        LOG.debug(f"Audio data analysis - Format: {audio_format}, Size: {len(audio_data)}, Header: {header_hex}")

        # 检查常见的音频文件头
        if audio_data.startswith(b'RIFF'):
            LOG.debug("Detected WAV/RIFF format")
        elif audio_data.startswith(b'\x1a\x45\xdf\xa3'):
            LOG.debug("Detected WebM/Matroska container")
        elif audio_data.startswith(b'OggS'):
            LOG.debug("Detected OGG container")
        elif audio_data.startswith(b'\xff\xfb') or audio_data.startswith(b'\xff\xf3'):
            LOG.debug("Detected MP3 format")
        elif audio_data.startswith(b'ftyp'):
            LOG.debug("Detected MP4 container")
        else:
            LOG.warning(f"Unknown audio format header: {header_hex[:8]}")

        # 检查是否包含静音数据
        if len(set(audio_data[:1000])) < 10:  # 前1000字节中不同值少于10个
            LOG.warning("Audio data appears to be mostly silent or invalid")

    def text_to_speech(self, text, voice="longxiaochun_v2", audio_format="mp3"):
        """
        语音合成：将文本转换为语音

        参数:
            text (str): 要合成的文本
            voice (str): 音色，默认使用龙小淳v2
            audio_format (str): 音频格式，支持 mp3, wav, pcm

        返回:
            bytes: 音频数据，失败时返回None
        """
        if not SPEECH_AVAILABLE:
            LOG.error("Speech synthesis not available - DashScope SDK not installed")
            return None

        if not text or not text.strip():
            LOG.error("Text is empty for speech synthesis")
            return None

        try:
            # 根据音频格式设置对应的AudioFormat
            format_mapping = {
                "mp3": AudioFormat.MP3_22050HZ_MONO_256KBPS,
                "wav": AudioFormat.WAV_22050HZ_MONO_16BIT,
                "pcm": AudioFormat.PCM_22050HZ_MONO_16BIT
            }

            audio_format_enum = format_mapping.get(audio_format, AudioFormat.MP3_22050HZ_MONO_256KBPS)

            # 创建语音合成器
            synthesizer = SpeechSynthesizer(
                model="cosyvoice-v2",
                voice=voice,
                format=audio_format_enum
            )

            # 同步调用语音合成
            audio_data = synthesizer.call(text)

            if audio_data:
                LOG.debug(f"Speech synthesis successful, audio size: {len(audio_data)} bytes")
                return audio_data
            else:
                LOG.error("Speech synthesis returned empty audio data")
                return None

        except Exception as e:
            LOG.error(f"Speech synthesis error: {str(e)}")
            return None

    def chat_with_voice(self, audio_data, audio_format="wav", sample_rate=16000,
                       voice="longxiaochun_v2", response_format="mp3", session_id=None):
        """
        语音对话：语音输入 -> 语音识别 -> 文本处理 -> 语音合成 -> 语音输出

        参数:
            audio_data (bytes): 输入的音频数据
            audio_format (str): 输入音频格式
            sample_rate (int): 输入音频采样率
            voice (str): 输出语音的音色
            response_format (str): 输出音频格式
            session_id (str): 会话ID

        返回:
            dict: 包含文本回复和音频数据的字典
                {
                    "text_input": "识别出的文本",
                    "text_response": "AI回复文本",
                    "audio_response": "音频数据(bytes)",
                    "success": True/False,
                    "error": "错误信息"
                }
        """
        result = {
            "text_input": "",
            "text_response": "",
            "audio_response": None,
            "success": False,
            "error": None
        }

        try:
            # 1. 语音识别
            LOG.debug(f"[{self.name}] Starting speech recognition...")
            text_input = self.speech_to_text(audio_data, audio_format, sample_rate)

            if not text_input:
                result["error"] = "Speech recognition failed"
                return result

            result["text_input"] = text_input
            LOG.info(f"[{self.name}] Speech recognized: {text_input}")

            # 2. 文本处理
            LOG.debug(f"[{self.name}] Processing text with AI...")
            text_response = self.chat_with_history(text_input, session_id)

            if not text_response:
                result["error"] = "AI text processing failed"
                return result

            result["text_response"] = text_response
            LOG.info(f"[{self.name}] AI response: {text_response}")

            # 3. 语音合成
            LOG.debug(f"[{self.name}] Starting speech synthesis...")
            # 移除HTML标签用于语音合成
            clean_text = text_response.replace('<br>', '\n').replace('<br/>', '\n')
            # 简单的HTML标签清理
            import re
            clean_text = re.sub(r'<[^>]+>', '', clean_text)

            audio_response = self.text_to_speech(clean_text, voice, response_format)

            if not audio_response:
                result["error"] = "Speech synthesis failed"
                return result

            result["audio_response"] = audio_response
            result["success"] = True

            LOG.info(f"[{self.name}] Voice chat completed successfully")
            return result

        except Exception as e:
            error_msg = f"Voice chat error: {str(e)}"
            LOG.error(f"[{self.name}] {error_msg}")
            result["error"] = error_msg
            return result
