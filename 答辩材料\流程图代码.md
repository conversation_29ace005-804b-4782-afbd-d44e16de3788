# LCA 法律咨询助手 - 流程图代码

## 1. 项目系统整体框架流程图

```mermaid
graph TB
    subgraph "用户层 User Layer"
        U1[Web浏览器用户]
        U2[移动端用户]
        U3[API调用用户]
    end
    
    subgraph "前端层 Frontend Layer"
        F1[HTML/CSS/JavaScript界面]
        F2[响应式设计组件]
        F3[实时交互模块]
        F4[语音录制组件]
    end
    
    subgraph "API网关层 API Gateway"
        G1[Flask RESTful API]
        G2[路由管理器]
        G3[请求验证器]
        G4[响应格式化器]
        G5[错误处理器]
        G6[CORS跨域处理]
    end
    
    subgraph "业务逻辑层 Business Logic Layer"
        subgraph "智能体管理器 Agent Manager"
            AM[AgentFactory智能体工厂]
            SM[SessionManager会话管理器]
        end
        
        subgraph "核心智能体 Core Agents"
            A1[ScenarioAgent<br/>场景咨询智能体]
            A2[ConversationAgent<br/>通用对话智能体]
            A3[VocabAgent<br/>法律学习智能体]
            A4[CaseSearchAgent<br/>案例检索智能体]
            A5[LawyerRecommendationAgent<br/>律师推荐智能体]
            A6[DocumentGenerationAgent<br/>文书生成智能体]
        end
    end
    
    subgraph "服务层 Service Layer"
        S1[LangChain框架服务]
        S2[会话历史服务]
        S3[提示词管理服务]
        S4[语音处理服务]
        S5[文档生成服务]
        S6[数据检索服务]
        S7[Web搜索服务]
    end
    
    subgraph "AI模型层 AI Model Layer"
        M1[千问大模型<br/>ChatTongyi]
        M2[Paraformer<br/>语音识别模型]
        M3[SpeechSynthesizer<br/>语音合成模型]
        M4[文本向量化模型]
    end
    
    subgraph "数据层 Data Layer"
        D1[会话历史存储<br/>InMemoryStore]
        D2[案例数据库<br/>Vector Database]
        D3[律师信息数据库]
        D4[法律文书模板库]
        D5[提示词库]
        D6[用户配置存储]
    end
    
    subgraph "外部服务 External Services"
        E1[阿里云DashScope API]
        E2[Web搜索引擎]
        E3[律师信息网站]
        E4[法律数据源]
    end
    
    %% 连接关系
    U1 --> F1
    U2 --> F1
    U3 --> G1
    
    F1 --> F2
    F2 --> F3
    F3 --> F4
    F1 --> G1
    
    G1 --> G2
    G2 --> G3
    G3 --> G4
    G4 --> G5
    G5 --> G6
    G1 --> AM
    
    AM --> A1
    AM --> A2
    AM --> A3
    AM --> A4
    AM --> A5
    AM --> A6
    SM --> A1
    SM --> A2
    SM --> A3
    SM --> A4
    SM --> A5
    SM --> A6
    
    A1 --> S1
    A2 --> S1
    A3 --> S1
    A4 --> S6
    A5 --> S7
    A6 --> S5
    
    S1 --> S2
    S1 --> S3
    S1 --> M1
    S4 --> M2
    S4 --> M3
    S6 --> M4
    
    S2 --> D1
    S3 --> D5
    S5 --> D4
    S6 --> D2
    S7 --> D3
    
    M1 --> E1
    M2 --> E1
    M3 --> E1
    S7 --> E2
    S7 --> E3
    S6 --> E4
    
    %% 样式定义
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef frontendClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef apiClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef businessClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef serviceClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef modelClass fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef dataClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef externalClass fill:#fafafa,stroke:#424242,stroke-width:2px
    
    class U1,U2,U3 userClass
    class F1,F2,F3,F4 frontendClass
    class G1,G2,G3,G4,G5,G6 apiClass
    class AM,SM,A1,A2,A3,A4,A5,A6 businessClass
    class S1,S2,S3,S4,S5,S6,S7 serviceClass
    class M1,M2,M3,M4 modelClass
    class D1,D2,D3,D4,D5,D6 dataClass
    class E1,E2,E3,E4 externalClass
```

## 2. 系统数据流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端界面
    participant API as Flask API
    participant Agent as 智能体
    participant LangChain as LangChain服务
    participant Model as 千问模型
    participant Storage as 数据存储
    
    User->>Frontend: 1. 访问系统
    Frontend->>API: 2. 发送请求
    API->>Agent: 3. 路由到对应智能体
    Agent->>Storage: 4. 获取会话历史
    Storage-->>Agent: 5. 返回历史记录
    Agent->>LangChain: 6. 构建提示词
    LangChain->>Model: 7. 调用大模型
    Model-->>LangChain: 8. 返回AI回复
    LangChain-->>Agent: 9. 处理模型输出
    Agent->>Storage: 10. 保存会话记录
    Agent-->>API: 11. 返回处理结果
    API-->>Frontend: 12. 格式化响应
    Frontend-->>User: 13. 展示结果
    
    Note over User,Storage: 完整的请求-响应流程
```

## 3. 律师推荐系统实现流程图

```mermaid
graph TB
    subgraph "用户交互层 User Interaction"
        U1[用户描述法律需求]
        U2[选择专业领域]
        U3[设置筛选条件]
        U4[查看推荐结果]
        U5[联系律师]
    end
    
    subgraph "律师推荐智能体 LawyerRecommendationAgent"
        LRA1[接收用户需求]
        LRA2[需求分析处理]
        LRA3[调用推荐算法]
        LRA4[结果排序优化]
        LRA5[格式化输出]
    end
    
    subgraph "需求分析模块 Requirement Analysis"
        RA1[文本预处理]
        RA2[关键词提取]
        RA3[专业领域识别]
        RA4[紧急程度评估]
        RA5[案件复杂度分析]
    end
    
    subgraph "律师数据获取 Lawyer Data Acquisition"
        LDA1[LawyerDataAPI数据接口]
        LDA2[Web搜索引擎]
        LDA3[律师网站爬取]
        LDA4[法律目录检索]
        LDA5[数据去重处理]
        LDA6[数据质量验证]
    end
    
    subgraph "推荐算法引擎 Recommendation Engine"
        RE1[专业匹配算法]
        RE2[经验评分算法]
        RE3[口碑评价算法]
        RE4[地理位置算法]
        RE5[费用评估算法]
        RE6[综合评分计算]
    end
    
    subgraph "数据存储层 Data Storage"
        DS1[律师基础信息]
        DS2[专业领域标签]
        DS3[案例成功率]
        DS4[用户评价数据]
        DS5[地理位置信息]
        DS6[收费标准数据]
    end
    
    subgraph "外部数据源 External Data Sources"
        EDS1[律师协会网站]
        EDS2[法律服务平台]
        EDS3[律师事务所官网]
        EDS4[法院判决书网站]
        EDS5[律师评价网站]
    end
    
    %% 主流程连接
    U1 --> LRA1
    U2 --> LRA1
    U3 --> LRA1
    LRA1 --> LRA2
    LRA2 --> RA1
    
    %% 需求分析流程
    RA1 --> RA2
    RA2 --> RA3
    RA3 --> RA4
    RA4 --> RA5
    RA5 --> LRA3
    
    %% 数据获取流程
    LRA3 --> LDA1
    LDA1 --> LDA2
    LDA2 --> LDA3
    LDA3 --> LDA4
    LDA4 --> LDA5
    LDA5 --> LDA6
    
    %% 推荐算法流程
    LDA6 --> RE1
    RA3 --> RE1
    RE1 --> RE2
    RE2 --> RE3
    RE3 --> RE4
    RE4 --> RE5
    RE5 --> RE6
    RE6 --> LRA4
    
    %% 数据存储连接
    LDA6 --> DS1
    DS1 --> DS2
    DS2 --> DS3
    DS3 --> DS4
    DS4 --> DS5
    DS5 --> DS6
    
    %% 外部数据源连接
    EDS1 --> LDA2
    EDS2 --> LDA3
    EDS3 --> LDA3
    EDS4 --> LDA4
    EDS5 --> LDA4
    
    %% 结果输出
    LRA4 --> LRA5
    LRA5 --> U4
    U4 --> U5
    
    %% 样式定义
    classDef userClass fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef agentClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef analysisClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef algorithmClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef storageClass fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef externalClass fill:#fafafa,stroke:#616161,stroke-width:2px
    
    class U1,U2,U3,U4,U5 userClass
    class LRA1,LRA2,LRA3,LRA4,LRA5 agentClass
    class RA1,RA2,RA3,RA4,RA5 analysisClass
    class LDA1,LDA2,LDA3,LDA4,LDA5,LDA6 dataClass
    class RE1,RE2,RE3,RE4,RE5,RE6 algorithmClass
    class DS1,DS2,DS3,DS4,DS5,DS6 storageClass
    class EDS1,EDS2,EDS3,EDS4,EDS5 externalClass
```

## 4. 律师推荐算法详细流程图

```mermaid
flowchart TD
    Start([开始律师推荐]) --> Input[接收用户需求描述]

    Input --> Parse[需求文本解析]
    Parse --> Extract[提取关键信息]

    subgraph "需求分析 Requirement Analysis"
        Extract --> Field[识别专业领域]
        Field --> Urgency[评估紧急程度]
        Urgency --> Complexity[分析案件复杂度]
        Complexity --> Location[确定地理位置]
        Location --> Budget[预算范围评估]
    end

    Budget --> GetLawyers[获取律师数据]

    subgraph "数据获取 Data Acquisition"
        GetLawyers --> WebSearch[Web搜索律师信息]
        WebSearch --> ScrapeData[爬取律师网站数据]
        ScrapeData --> ValidateData[数据验证和清洗]
        ValidateData --> DeduplicateData[去重处理]
    end

    DeduplicateData --> StartScoring[开始评分计算]

    subgraph "评分算法 Scoring Algorithm"
        StartScoring --> SpecialtyScore[专业匹配评分]
        SpecialtyScore --> ExperienceScore[经验评分]
        ExperienceScore --> ReputationScore[口碑评分]
        ReputationScore --> LocationScore[地理位置评分]
        LocationScore --> CostScore[费用评分]
        CostScore --> WeightedSum[加权综合评分]
    end

    WeightedSum --> Sort[按评分排序]
    Sort --> Filter[应用筛选条件]
    Filter --> TopN[选择Top-N律师]

    subgraph "结果优化 Result Optimization"
        TopN --> Diversify[结果多样化处理]
        Diversify --> AddDetails[补充详细信息]
        AddDetails --> FormatOutput[格式化输出]
    end

    FormatOutput --> Return[返回推荐结果]
    Return --> End([结束])

    %% 决策节点
    GetLawyers --> HasCache{是否有缓存数据?}
    HasCache -->|是| UseCache[使用缓存数据]
    HasCache -->|否| WebSearch
    UseCache --> StartScoring

    Filter --> EnoughResults{结果数量足够?}
    EnoughResults -->|是| TopN
    EnoughResults -->|否| ExpandSearch[扩大搜索范围]
    ExpandSearch --> GetLawyers

    %% 样式定义
    classDef processClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef algorithmClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef startEndClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px

    class Start,End startEndClass
    class HasCache,EnoughResults decisionClass
    class GetLawyers,WebSearch,ScrapeData,ValidateData,DeduplicateData,UseCache,ExpandSearch dataClass
    class SpecialtyScore,ExperienceScore,ReputationScore,LocationScore,CostScore,WeightedSum algorithmClass
```

## 5. 律师推荐系统时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端界面
    participant API as Flask API
    participant LRAgent as 律师推荐智能体
    participant DataAPI as 律师数据API
    participant WebSearch as Web搜索引擎
    participant Algorithm as 推荐算法
    participant Database as 数据库

    User->>Frontend: 1. 输入法律需求
    Frontend->>API: 2. 发送推荐请求
    API->>LRAgent: 3. 调用律师推荐智能体

    LRAgent->>LRAgent: 4. 解析用户需求
    LRAgent->>Database: 5. 检查缓存数据

    alt 缓存未命中
        LRAgent->>DataAPI: 6. 请求律师数据
        DataAPI->>WebSearch: 7. 搜索律师信息
        WebSearch-->>DataAPI: 8. 返回搜索结果
        DataAPI->>DataAPI: 9. 数据清洗和验证
        DataAPI-->>LRAgent: 10. 返回律师数据
        LRAgent->>Database: 11. 缓存律师数据
    else 缓存命中
        Database-->>LRAgent: 6. 返回缓存数据
    end

    LRAgent->>Algorithm: 12. 调用推荐算法
    Algorithm->>Algorithm: 13. 计算专业匹配度
    Algorithm->>Algorithm: 14. 计算经验评分
    Algorithm->>Algorithm: 15. 计算口碑评分
    Algorithm->>Algorithm: 16. 综合评分排序
    Algorithm-->>LRAgent: 17. 返回推荐结果

    LRAgent->>LRAgent: 18. 格式化输出结果
    LRAgent-->>API: 19. 返回推荐律师列表
    API-->>Frontend: 20. 返回JSON响应
    Frontend-->>User: 21. 展示推荐律师

    User->>Frontend: 22. 选择律师查看详情
    Frontend->>API: 23. 请求律师详细信息
    API->>Database: 24. 查询律师详情
    Database-->>API: 25. 返回详细信息
    API-->>Frontend: 26. 返回律师详情
    Frontend-->>User: 27. 展示律师详细信息

    Note over User,Database: 完整的律师推荐流程
```

## 6. 核心算法伪代码

### 6.1 律师推荐算法伪代码

```python
def recommend_lawyers(user_requirement, filters=None):
    """
    律师推荐主算法
    """
    # 1. 需求分析
    analyzed_requirement = analyze_requirement(user_requirement)

    # 2. 获取律师数据
    lawyers = get_lawyer_data(analyzed_requirement.field)

    # 3. 计算评分
    scored_lawyers = []
    for lawyer in lawyers:
        # 专业匹配评分 (权重: 40%)
        specialty_score = calculate_specialty_match(
            analyzed_requirement.field,
            lawyer.specialties
        )

        # 经验评分 (权重: 30%)
        experience_score = calculate_experience_score(
            lawyer.years_of_practice,
            lawyer.case_count,
            lawyer.success_rate
        )

        # 口碑评分 (权重: 20%)
        reputation_score = calculate_reputation_score(
            lawyer.rating,
            lawyer.review_count,
            lawyer.client_feedback
        )

        # 地理位置评分 (权重: 10%)
        location_score = calculate_location_score(
            analyzed_requirement.location,
            lawyer.location
        )

        # 综合评分
        total_score = (
            specialty_score * 0.4 +
            experience_score * 0.3 +
            reputation_score * 0.2 +
            location_score * 0.1
        )

        scored_lawyers.append((lawyer, total_score))

    # 4. 排序和筛选
    scored_lawyers.sort(key=lambda x: x[1], reverse=True)

    # 5. 应用筛选条件
    if filters:
        scored_lawyers = apply_filters(scored_lawyers, filters)

    # 6. 返回Top-N结果
    return [lawyer for lawyer, score in scored_lawyers[:10]]

def analyze_requirement(requirement_text):
    """
    分析用户需求
    """
    return {
        'field': extract_legal_field(requirement_text),
        'urgency': assess_urgency(requirement_text),
        'complexity': assess_complexity(requirement_text),
        'location': extract_location(requirement_text),
        'budget': extract_budget(requirement_text)
    }

def calculate_specialty_match(required_field, lawyer_specialties):
    """
    计算专业匹配度
    """
    if required_field in lawyer_specialties:
        return 1.0

    # 计算相关专业的匹配度
    related_score = 0.0
    for specialty in lawyer_specialties:
        similarity = calculate_field_similarity(required_field, specialty)
        related_score = max(related_score, similarity)

    return related_score

def calculate_experience_score(years, cases, success_rate):
    """
    计算经验评分
    """
    # 年限评分 (0-1)
    years_score = min(years / 20, 1.0)

    # 案例数量评分 (0-1)
    cases_score = min(cases / 1000, 1.0)

    # 成功率评分 (0-1)
    success_score = success_rate / 100

    return (years_score * 0.4 + cases_score * 0.3 + success_score * 0.3)
```

## 使用说明

1. **Mermaid图表渲染**：
   - 可以在支持Mermaid的Markdown编辑器中直接渲染
   - 推荐使用Typora、VS Code (Mermaid插件)、GitHub等
   - 在线渲染：https://mermaid.live/

2. **PPT制作建议**：
   - 将流程图导出为PNG/SVG格式插入PPT
   - 可以分步骤展示，增加动画效果
   - 配合文字说明，突出关键技术点

3. **答辩展示技巧**：
   - 先展示整体架构，再深入具体模块
   - 重点讲解律师推荐算法的创新点
   - 结合实际代码演示系统运行效果
