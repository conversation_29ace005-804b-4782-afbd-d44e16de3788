#!/usr/bin/env python3
"""
演示AI推荐功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def demo_ai_recommendation():
    """演示AI推荐功能"""
    print("🎯 律师推荐系统 - AI推荐功能演示")
    print("="*60)
    
    try:
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        
        # 创建推荐代理
        agent = LawyerRecommendationAgent()
        print("✓ 律师推荐代理初始化成功")
        
        # 演示案例
        demo_case = {
            "request": "我需要处理离婚财产分割问题，涉及房产和股权分割",
            "description": "婚姻家庭法 + 财产分割案例"
        }
        
        print(f"\n📋 演示案例: {demo_case['description']}")
        print(f"用户需求: {demo_case['request']}")
        print("\n🔄 正在生成推荐...")
        
        # 处理推荐请求
        response = agent.process_recommendation_request(
            demo_case['request'], 
            "demo_session"
        )
        
        # 分析响应内容
        print("\n📊 推荐结果分析:")
        
        # 检查是否包含AI推荐
        if "AI智能推荐" in response:
            print("✅ 包含AI智能推荐")
        else:
            print("❌ 缺少AI智能推荐")
        
        # 检查是否包含数据库匹配
        if "数据库匹配推荐" in response:
            print("✅ 包含数据库匹配推荐")
        else:
            print("❌ 缺少数据库匹配推荐")
        
        # 检查是否包含AI推荐标识
        if "🤖" in response:
            print("✅ 包含AI推荐标识")
        else:
            print("❌ 缺少AI推荐标识")
        
        # 检查是否包含AI建议
        if "AI建议" in response:
            print("✅ 包含AI个性化建议")
        else:
            print("❌ 缺少AI个性化建议")
        
        # 显示推荐结果摘要
        print("\n📝 推荐结果摘要:")
        lines = response.split('<br>')
        summary_lines = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('📝') and not line.startswith('🏢'):
                summary_lines.append(line)
        
        # 显示前15行摘要
        for i, line in enumerate(summary_lines[:15], 1):
            print(f"{i:2d}. {line}")
        
        if len(summary_lines) > 15:
            print("    ...")
            print(f"    (共 {len(summary_lines)} 行推荐内容)")
        
        # 统计推荐团队数量
        if "为您找到" in response:
            import re
            match = re.search(r'为您找到 \*\*(\d+)\*\* 个优质律师团队', response)
            if match:
                total_teams = int(match.group(1))
                print(f"\n📈 推荐统计: 共推荐 {total_teams} 个律师团队")
        
        # 检查数据来源分布
        if "数据来源：" in response:
            import re
            source_match = re.search(r'数据库匹配 (\d+) 个 \+ AI智能推荐 (\d+) 个', response)
            if source_match:
                db_count = int(source_match.group(1))
                ai_count = int(source_match.group(2))
                print(f"📊 数据来源: 数据库匹配 {db_count} 个, AI推荐 {ai_count} 个")
        
        print("\n🎉 AI推荐功能演示完成！")
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_feature_comparison():
    """显示功能对比"""
    print("\n" + "="*60)
    print("🔄 功能对比：修复前 vs 修复后")
    print("="*60)
    
    comparison = [
        ("推荐数据来源", "❌ 仅静态JSON数据", "✅ 网络爬虫 + AI推荐 + 法律目录"),
        ("推荐方式", "❌ 简单匹配", "✅ 智能推荐引擎 + AI分析"),
        ("推荐数量", "❌ 固定几个", "✅ 动态8个（数据库+AI）"),
        ("推荐质量", "❌ 模拟数据", "✅ 真实律师事务所"),
        ("个性化程度", "❌ 无个性化", "✅ AI个性化建议"),
        ("数据实时性", "❌ 静态数据", "✅ 实时网络搜索"),
        ("推荐理由", "❌ 无推荐理由", "✅ 详细推荐理由"),
        ("AI参与度", "❌ 无AI参与", "✅ 全程AI参与")
    ]
    
    print(f"{'功能':<12} {'修复前':<20} {'修复后':<30}")
    print("-" * 60)
    
    for feature, before, after in comparison:
        print(f"{feature:<12} {before:<20} {after:<30}")
    
    print("\n💡 主要改进:")
    print("1. 🌐 集成真实网络数据源，获取最新律师信息")
    print("2. 🤖 AI智能推荐，基于专业知识推荐知名律师事务所")
    print("3. 📊 多维度推荐，结合数据库匹配和AI推荐")
    print("4. 💬 个性化建议，AI提供选择建议和注意事项")
    print("5. 🔍 专业搜索，支持按专业领域和地区搜索")

def main():
    """主函数"""
    print("🏛️ 律师推荐系统 - AI推荐功能修复演示")
    print("="*60)
    
    # 运行演示
    success = demo_ai_recommendation()
    
    # 显示功能对比
    show_feature_comparison()
    
    print("\n" + "="*60)
    if success:
        print("🎉 演示成功！AI推荐功能已完全修复并正常工作。")
        print("\n📋 下一步建议:")
        print("1. 启动完整系统: python main_html.py")
        print("2. 访问律师推荐页面进行实际测试")
        print("3. 输入具体法律需求，体验AI推荐功能")
    else:
        print("❌ 演示失败，请检查系统配置。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
