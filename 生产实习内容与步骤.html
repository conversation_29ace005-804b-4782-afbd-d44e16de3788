<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产实习内容与步骤 - LCA法律咨询助手系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .section {
            margin-bottom: 40px;
            border-left: 4px solid #2a5298;
            padding-left: 20px;
        }
        .section h2 {
            color: #1e3c72;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .section h3 {
            color: #2a5298;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }
        .section h4 {
            color: #4a6fa5;
            font-size: 1.2em;
            margin: 20px 0 10px 0;
        }
        .highlight-box {
            background: #f8f9ff;
            border: 1px solid #e3e8ff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .tech-item {
            background: #f5f7ff;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #2a5298;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .architecture-diagram {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        ul, ol {
            padding-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
        .code-block {
            background: #f4f4f4;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .badge {
            display: inline-block;
            background: #2a5298;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            margin: 2px;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #2a5298;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #2a5298;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>生产实习内容与步骤</h1>
            <p>LCA法律咨询助手系统开发实践</p>
        </div>
        
        <div class="content">
            <!-- 1. 项目概述 -->
            <div class="section">
                <h2>一、项目概述</h2>
                
                <div class="highlight-box">
                    <h3>项目名称</h3>
                    <p><strong>LCA (LegalConsultationAssistant) 法律咨询助手系统</strong></p>
                </div>

                <h3>项目背景</h3>
                <p>随着人工智能技术的快速发展，法律服务行业正面临数字化转型的重要机遇。传统的法律咨询服务存在成本高、效率低、覆盖面有限等问题，普通民众在遇到法律问题时往往难以获得及时、专业的法律指导。</p>

                <h3>项目目标</h3>
                <ul>
                    <li>构建基于大语言模型的智能法律咨询系统</li>
                    <li>提供24/7全天候法律问题解答服务</li>
                    <li>降低法律咨询门槛，提高法律服务可及性</li>
                    <li>实现场景化、专业化的法律咨询体验</li>
                    <li>集成律师推荐、文书生成、案例检索等增值服务</li>
                </ul>

                <h3>项目特色</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>🎯 场景化咨询</h4>
                        <p>针对婚姻纠纷、合同纠纷、工伤赔偿三大高频法律场景提供专业咨询</p>
                    </div>
                    <div class="feature-item">
                        <h4>🤖 智能问答</h4>
                        <p>基于通义千问大模型的开放式法律问题解答</p>
                    </div>
                    <div class="feature-item">
                        <h4>📚 法律学习</h4>
                        <p>系统化学习宪法、民法典、刑法三部核心法律</p>
                    </div>
                    <div class="feature-item">
                        <h4>🔍 案例检索</h4>
                        <p>基于真实判决案例的智能检索和分析</p>
                    </div>
                    <div class="feature-item">
                        <h4>👨‍💼 律师推荐</h4>
                        <p>智能匹配专业律师团队推荐服务</p>
                    </div>
                    <div class="feature-item">
                        <h4>📄 文书生成</h4>
                        <p>自动化生成起诉状等法律文书</p>
                    </div>
                </div>
            </div>

            <!-- 2. 需求分析 -->
            <div class="section">
                <h2>二、项目需求分析</h2>

                <h3>2.1 功能性需求</h3>
                
                <h4>核心功能模块</h4>
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>场景咨询模块</h4>
                        <ul>
                            <li>婚姻纠纷咨询：离婚程序、财产分割、子女抚养</li>
                            <li>合同纠纷咨询：合同效力、违约责任、争议解决</li>
                            <li>工伤赔偿咨询：工伤认定、伤残鉴定、赔偿计算</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>智能问答模块</h4>
                        <ul>
                            <li>自然语言理解和处理</li>
                            <li>法律知识库查询</li>
                            <li>个性化回答生成</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>法律学习模块</h4>
                        <ul>
                            <li>分章节学习宪法、民法典、刑法</li>
                            <li>交互式问答学习</li>
                            <li>学习进度跟踪</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>案例检索模块</h4>
                        <ul>
                            <li>关键词和罪名类型检索</li>
                            <li>案例详情展示</li>
                            <li>相关法条关联</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>律师推荐模块</h4>
                        <ul>
                            <li>基于需求的智能匹配</li>
                            <li>律师团队信息展示</li>
                            <li>专业领域筛选</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>文书生成模块</h4>
                        <ul>
                            <li>起诉状自动生成</li>
                            <li>模板化文书制作</li>
                            <li>个性化信息填充</li>
                        </ul>
                    </div>
                </div>

                <h3>2.2 非功能性需求</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>性能需求</h4>
                        <ul>
                            <li>响应时间 < 3秒</li>
                            <li>并发用户数 > 100</li>
                            <li>系统可用性 > 99%</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>安全需求</h4>
                        <ul>
                            <li>用户数据加密存储</li>
                            <li>API接口安全认证</li>
                            <li>敏感信息脱敏处理</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>可用性需求</h4>
                        <ul>
                            <li>直观的用户界面设计</li>
                            <li>响应式布局支持</li>
                            <li>多终端兼容性</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>扩展性需求</h4>
                        <ul>
                            <li>模块化架构设计</li>
                            <li>支持新场景扩展</li>
                            <li>API接口标准化</li>
                        </ul>
                    </div>
                </div>

                <h3>2.3 用户需求分析</h3>
                <div class="highlight-box">
                    <h4>目标用户群体</h4>
                    <ul>
                        <li><span class="badge">普通民众</span> 需要法律咨询但缺乏专业知识</li>
                        <li><span class="badge">法律学习者</span> 希望系统学习法律知识</li>
                        <li><span class="badge">基层法务</span> 需要快速获取法律参考</li>
                        <li><span class="badge">中小企业</span> 需要合同和劳动法咨询</li>
                    </ul>
                </div>
            </div>

            <!-- 3. 总体设计 -->
            <div class="section">
                <h2>三、项目总体设计</h2>

                <h3>3.1 系统架构设计</h3>
                <div class="architecture-diagram">
                    <h4>分层架构模式</h4>
                    <div class="code-block">
┌─────────────────────────────────────────┐
│           前端展示层 (Web UI)              │
│     HTML5 + CSS3 + JavaScript           │
└─────────────────────────────────────────┘
                    ↕ HTTP/AJAX
┌─────────────────────────────────────────┐
│          API接口层 (Flask)               │
│        RESTful API + CORS               │
└─────────────────────────────────────────┘
                    ↕ 函数调用
┌─────────────────────────────────────────┐
│         业务逻辑层 (智能体系统)            │
│   Agent-Based Architecture             │
└─────────────────────────────────────────┘
                    ↕ API调用
┌─────────────────────────────────────────┐
│        AI服务层 (通义千问大模型)           │
│      LangChain + ChatTongyi             │
└─────────────────────────────────────────┘
                    </div>
                </div>

                <h3>3.2 技术栈选型</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>🎨 前端技术</h4>
                        <ul>
                            <li><span class="badge">HTML5</span> 语义化标记</li>
                            <li><span class="badge">CSS3</span> 响应式样式</li>
                            <li><span class="badge">JavaScript</span> 交互逻辑</li>
                            <li><span class="badge">AJAX</span> 异步通信</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>⚙️ 后端技术</h4>
                        <ul>
                            <li><span class="badge">Python 3.8+</span> 开发语言</li>
                            <li><span class="badge">Flask</span> Web框架</li>
                            <li><span class="badge">Flask-CORS</span> 跨域支持</li>
                            <li><span class="badge">Gunicorn</span> WSGI服务器</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🤖 AI技术栈</h4>
                        <ul>
                            <li><span class="badge">LangChain</span> AI应用框架</li>
                            <li><span class="badge">通义千问</span> 大语言模型</li>
                            <li><span class="badge">DashScope</span> AI服务SDK</li>
                            <li><span class="badge">Jieba</span> 中文分词</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🛠️ 工具库</h4>
                        <ul>
                            <li><span class="badge">python-docx</span> 文档处理</li>
                            <li><span class="badge">requests</span> HTTP客户端</li>
                            <li><span class="badge">loguru</span> 日志管理</li>
                            <li><span class="badge">python-dotenv</span> 环境配置</li>
                        </ul>
                    </div>
                </div>
                <h3>3.3 智能体架构设计</h3>
                <div class="highlight-box">
                    <h4>Agent-Based设计模式</h4>
                    <p>采用智能体模式，每个智能体负责特定的业务领域，具有独立的提示词、会话历史和处理逻辑。</p>
                </div>

                <div class="feature-list">
                    <div class="feature-item">
                        <h4>🎭 ScenarioAgent</h4>
                        <p><strong>场景智能体</strong></p>
                        <ul>
                            <li>婚姻纠纷专家</li>
                            <li>合同纠纷专家</li>
                            <li>工伤赔偿专家</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>💬 ConversationAgent</h4>
                        <p><strong>对话智能体</strong></p>
                        <ul>
                            <li>通用法律问答</li>
                            <li>上下文理解</li>
                            <li>多轮对话管理</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>📖 VocabAgent</h4>
                        <p><strong>学习智能体</strong></p>
                        <ul>
                            <li>法律条文解释</li>
                            <li>知识点问答</li>
                            <li>学习进度跟踪</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🔍 CaseSearchAgent</h4>
                        <p><strong>案例检索智能体</strong></p>
                        <ul>
                            <li>案例智能搜索</li>
                            <li>结果分析总结</li>
                            <li>相关法条匹配</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>👨‍💼 LawyerRecommendationAgent</h4>
                        <p><strong>律师推荐智能体</strong></p>
                        <ul>
                            <li>需求分析匹配</li>
                            <li>律师信息整合</li>
                            <li>推荐算法优化</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>📄 DocumentGenerationAgent</h4>
                        <p><strong>文书生成智能体</strong></p>
                        <ul>
                            <li>模板化生成</li>
                            <li>信息自动填充</li>
                            <li>格式标准化</li>
                        </ul>
                    </div>
                </div>

                <h3>3.4 数据流程设计</h3>
                <div class="architecture-diagram">
                    <h4>系统数据流程图</h4>
                    <div class="code-block">
用户输入 → 前端界面 → API路由 → 智能体选择 → 提示词处理
    ↓
大模型调用 → 结果处理 → 格式化输出 → API响应 → 前端展示
    ↓
会话历史存储 ← 日志记录 ← 错误处理 ← 结果验证
                    </div>
                </div>

                <h3>3.5 核心算法设计</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>🧠 自然语言处理</h4>
                        <ul>
                            <li>基于Transformer的语言理解</li>
                            <li>上下文感知的对话管理</li>
                            <li>意图识别和实体抽取</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🎯 智能推荐算法</h4>
                        <ul>
                            <li>基于内容的协同过滤</li>
                            <li>用户画像构建</li>
                            <li>多维度相似度计算</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔍 检索算法</h4>
                        <ul>
                            <li>TF-IDF文本相似度</li>
                            <li>语义向量检索</li>
                            <li>多关键词组合查询</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>📝 文本生成算法</h4>
                        <ul>
                            <li>模板驱动生成</li>
                            <li>条件文本生成</li>
                            <li>格式化后处理</li>
                        </ul>
                    </div>
                </div>

                <h3>3.6 安全性设计</h3>
                <div class="highlight-box">
                    <h4>安全措施</h4>
                    <ul>
                        <li><strong>API安全</strong>：请求频率限制、参数验证、错误信息脱敏</li>
                        <li><strong>数据安全</strong>：敏感信息加密、会话数据定期清理</li>
                        <li><strong>输入安全</strong>：XSS防护、SQL注入防护、输入长度限制</li>
                        <li><strong>访问控制</strong>：CORS配置、API密钥管理、日志审计</li>
                    </ul>
                </div>
            </div>

            <!-- 4. 实施步骤 -->
            <div class="section">
                <h2>四、项目实施步骤</h2>

                <h3>4.1 开发阶段规划</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>第一阶段：基础架构搭建 (1-2周)</h4>
                        <ul>
                            <li>项目环境配置和依赖安装</li>
                            <li>Flask后端框架搭建</li>
                            <li>基础智能体抽象类设计</li>
                            <li>前端基础界面开发</li>
                            <li>API接口规范定义</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>第二阶段：核心功能开发 (3-4周)</h4>
                        <ul>
                            <li>场景咨询智能体开发</li>
                            <li>通用问答智能体开发</li>
                            <li>会话管理系统实现</li>
                            <li>前端交互界面完善</li>
                            <li>API接口联调测试</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>第三阶段：扩展功能开发 (2-3周)</h4>
                        <ul>
                            <li>法律学习模块开发</li>
                            <li>案例检索功能实现</li>
                            <li>律师推荐系统开发</li>
                            <li>文书生成功能实现</li>
                            <li>语音交互功能集成</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>第四阶段：系统优化测试 (1-2周)</h4>
                        <ul>
                            <li>性能优化和缓存机制</li>
                            <li>错误处理和异常管理</li>
                            <li>用户体验优化</li>
                            <li>系统集成测试</li>
                            <li>部署和上线准备</li>
                        </ul>
                    </div>
                </div>

                <h3>4.2 技术实现要点</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>🏗️ 架构实现</h4>
                        <ul>
                            <li>采用MVC设计模式</li>
                            <li>智能体工厂模式</li>
                            <li>依赖注入管理</li>
                            <li>配置文件外部化</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔧 开发工具</h4>
                        <ul>
                            <li>VS Code开发环境</li>
                            <li>Git版本控制</li>
                            <li>Postman API测试</li>
                            <li>Chrome DevTools调试</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>📊 监控运维</h4>
                        <ul>
                            <li>日志系统集成</li>
                            <li>性能监控指标</li>
                            <li>错误报警机制</li>
                            <li>健康检查接口</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🧪 测试策略</h4>
                        <ul>
                            <li>单元测试覆盖</li>
                            <li>集成测试验证</li>
                            <li>用户体验测试</li>
                            <li>压力测试评估</li>
                        </ul>
                    </div>
                </div>

                <h3>4.3 质量保证措施</h3>
                <div class="highlight-box">
                    <h4>代码质量</h4>
                    <ul>
                        <li><strong>编码规范</strong>：遵循PEP8 Python编码规范</li>
                        <li><strong>代码审查</strong>：关键模块代码review机制</li>
                        <li><strong>文档完善</strong>：API文档、部署文档、用户手册</li>
                        <li><strong>版本管理</strong>：Git分支管理和版本标签</li>
                    </ul>
                </div>

                <h3>4.4 风险控制</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>⚠️ 技术风险</h4>
                        <ul>
                            <li>API调用频率限制</li>
                            <li>大模型响应延迟</li>
                            <li>第三方服务依赖</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>📈 性能风险</h4>
                        <ul>
                            <li>并发访问压力</li>
                            <li>内存使用优化</li>
                            <li>数据库查询效率</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🔒 安全风险</h4>
                        <ul>
                            <li>用户输入验证</li>
                            <li>API密钥泄露</li>
                            <li>敏感数据保护</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>👥 业务风险</h4>
                        <ul>
                            <li>法律咨询准确性</li>
                            <li>用户期望管理</li>
                            <li>合规性要求</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 5. 预期成果 -->
            <div class="section">
                <h2>五、预期成果与价值</h2>

                <h3>5.1 技术成果</h3>
                <div class="highlight-box">
                    <ul>
                        <li>完整的法律咨询AI系统原型</li>
                        <li>可扩展的智能体架构框架</li>
                        <li>标准化的API接口规范</li>
                        <li>完善的技术文档和部署指南</li>
                    </ul>
                </div>

                <h3>5.2 应用价值</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>🎯 社会价值</h4>
                        <ul>
                            <li>降低法律服务门槛</li>
                            <li>提高法律知识普及</li>
                            <li>促进司法公平正义</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>💼 商业价值</h4>
                        <ul>
                            <li>法律科技产品原型</li>
                            <li>AI应用场景验证</li>
                            <li>市场需求调研</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🎓 教育价值</h4>
                        <ul>
                            <li>AI技术实践经验</li>
                            <li>软件工程能力</li>
                            <li>项目管理经验</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔬 研究价值</h4>
                        <ul>
                            <li>法律AI应用研究</li>
                            <li>人机交互优化</li>
                            <li>智能推荐算法</li>
                        </ul>
                    </div>
                </div>

                <h3>5.3 技能提升</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>💻 技术技能</h4>
                        <ul>
                            <li>Python Web开发</li>
                            <li>AI大模型应用</li>
                            <li>前端交互设计</li>
                            <li>API接口设计</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🏗️ 工程技能</h4>
                        <ul>
                            <li>系统架构设计</li>
                            <li>模块化开发</li>
                            <li>代码质量管理</li>
                            <li>性能优化</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>📊 项目技能</h4>
                        <ul>
                            <li>需求分析</li>
                            <li>项目规划</li>
                            <li>风险控制</li>
                            <li>文档编写</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🤝 协作技能</h4>
                        <ul>
                            <li>团队协作</li>
                            <li>沟通表达</li>
                            <li>问题解决</li>
                            <li>持续学习</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 4. 项目详细设计 -->
            <div class="section">
                <h2>四、项目详细设计</h2>

                <h3>4.1 律师推荐模块详细设计</h3>
                <div class="highlight-box">
                    <h4>🎯 模块概述</h4>
                    <p>律师推荐模块是LCA系统的核心功能之一，采用<strong>基于内容的推荐算法</strong>，结合<strong>多数据源融合</strong>和<strong>智能匹配规则</strong>，为用户提供精准的律师团队推荐服务。</p>
                </div>

                <h4>4.1.1 推荐引擎架构</h4>
                <div class="architecture-diagram">
                    <h4>推荐引擎工作流程</h4>
                    <div class="code-block">
用户需求输入 → 特征提取 → 向量化处理 → 相似度计算 → 规则调整 → 结果排序 → 推荐输出
    ↓              ↓           ↓           ↓           ↓           ↓           ↓
文本预处理 → 关键词提取 → TF-IDF权重 → 余弦相似度 → 多维度加权 → 分数排序 → 格式化展示
    ↓              ↓           ↓           ↓           ↓           ↓           ↓
停用词过滤 → 领域词典 → 特征向量 → 匹配计算 → 经验评分 → Top-N选择 → 结果返回
                    </div>
                </div>

                <h4>4.1.2 核心算法实现</h4>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>🔍 特征提取算法</h4>
                        <ul>
                            <li><strong>文本预处理</strong>：使用jieba分词进行中文分词</li>
                            <li><strong>关键词提取</strong>：TF-IDF算法提取关键特征</li>
                            <li><strong>领域权重</strong>：法律专业词汇加权处理</li>
                            <li><strong>停用词过滤</strong>：去除无意义词汇</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>📊 相似度计算</h4>
                        <ul>
                            <li><strong>余弦相似度</strong>：计算用户需求与律师特征的相似度</li>
                            <li><strong>向量空间模型</strong>：多维特征向量表示</li>
                            <li><strong>权重归一化</strong>：特征权重标准化处理</li>
                            <li><strong>相似度阈值</strong>：设置最低匹配阈值</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>⚖️ 匹配规则引擎</h4>
                        <ul>
                            <li><strong>专业领域匹配</strong>：精确匹配法律专业领域</li>
                            <li><strong>地域位置匹配</strong>：地理位置就近原则</li>
                            <li><strong>经验年限加权</strong>：资深律师优先推荐</li>
                            <li><strong>评分等级调整</strong>：高评分律师权重提升</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔄 多源数据融合</h4>
                        <ul>
                            <li><strong>本地数据库</strong>：预置优质律师团队数据</li>
                            <li><strong>网络API</strong>：实时获取最新律师信息</li>
                            <li><strong>AI生成推荐</strong>：大模型智能推荐补充</li>
                            <li><strong>数据去重</strong>：多源数据智能去重处理</li>
                        </ul>
                    </div>
                </div>

                <h4>4.1.3 推荐算法核心代码</h4>
                <div class="code-block">
def recommend(self, user_requirements: str, lawyer_teams: List[Dict], top_n: int = 5):
    """基于内容的律师推荐算法"""

    # 1. 提取用户需求特征向量
    user_vector = self._extract_features(user_requirements)

    # 2. 计算每个律师团队的相似度
    team_similarities = []
    for team in lawyer_teams:
        # 提取律师团队特征
        team_vector = self._extract_team_features(team)

        # 计算余弦相似度
        similarity = self._calculate_similarity(user_vector, team_vector)

        # 应用匹配规则调整
        similarity = self._apply_matching_rules(similarity, user_requirements, team)

        team_similarities.append({"team": team, "similarity": similarity})

    # 3. 按相似度排序并返回Top-N结果
    team_similarities.sort(key=lambda x: x["similarity"], reverse=True)
    return [item["team"] for item in team_similarities[:top_n]]
                </div>

                <h4>4.1.4 数据源设计</h4>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>📋 本地数据库</h4>
                        <ul>
                            <li>预置50+优质律师事务所</li>
                            <li>包含专业领域、地理位置、评分等信息</li>
                            <li>定期更新维护数据质量</li>
                            <li>支持快速本地查询</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>� 网络API数据源</h4>
                        <ul>
                            <li>律师目录API接口</li>
                            <li>法律服务平台数据</li>
                            <li>实时搜索引擎结果</li>
                            <li>第三方律师评价数据</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🤖 AI智能推荐</h4>
                        <ul>
                            <li>大模型生成推荐结果</li>
                            <li>基于知识库的智能匹配</li>
                            <li>个性化推荐建议</li>
                            <li>动态调整推荐策略</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🔄 数据融合策略</h4>
                        <ul>
                            <li>多源数据权重分配</li>
                            <li>智能去重算法</li>
                            <li>数据质量评估</li>
                            <li>实时缓存机制</li>
                        </ul>
                    </div>
                </div>

                <h4>4.1.5 API接口设计</h4>
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>GET /api/lawyer_recommendation/intro</h4>
                        <p><strong>功能</strong>：获取律师推荐服务介绍</p>
                        <p><strong>返回</strong>：服务介绍文本和使用指南</p>
                    </div>
                    <div class="timeline-item">
                        <h4>POST /api/lawyer_recommendation/start</h4>
                        <p><strong>功能</strong>：开始律师推荐会话</p>
                        <p><strong>参数</strong>：session_id（可选）</p>
                        <p><strong>返回</strong>：会话ID和初始化信息</p>
                    </div>
                    <div class="timeline-item">
                        <h4>POST /api/lawyer_recommendation/chat</h4>
                        <p><strong>功能</strong>：处理用户推荐请求</p>
                        <p><strong>参数</strong>：message（用户需求）, session_id</p>
                        <p><strong>返回</strong>：推荐结果和个性化建议</p>
                    </div>
                </div>

                <h3>4.2 其他核心模块设计</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>📄 文书生成模块</h4>
                        <ul>
                            <li>模板驱动的文档生成</li>
                            <li>智能信息收集流程</li>
                            <li>专业法律文书格式</li>
                            <li>Word文档自动生成</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔍 案例检索模块</h4>
                        <ul>
                            <li>关键词智能搜索</li>
                            <li>罪名类型分类检索</li>
                            <li>案例详情展示</li>
                            <li>相关法条关联</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🎭 场景咨询模块</h4>
                        <ul>
                            <li>专业场景智能体</li>
                            <li>上下文感知对话</li>
                            <li>专业提示词优化</li>
                            <li>多轮对话管理</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>📚 法律学习模块</h4>
                        <ul>
                            <li>分章节学习体系</li>
                            <li>交互式问答学习</li>
                            <li>学习进度跟踪</li>
                            <li>知识点测试评估</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 5. 项目实现 -->
            <div class="section">
                <h2>五、项目实现</h2>

                <h3>5.1 开发环境搭建</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>💻 开发工具</h4>
                        <ul>
                            <li><strong>IDE</strong>：Visual Studio Code</li>
                            <li><strong>版本控制</strong>：Git + GitHub</li>
                            <li><strong>API测试</strong>：Postman</li>
                            <li><strong>浏览器调试</strong>：Chrome DevTools</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🐍 Python环境</h4>
                        <ul>
                            <li><strong>Python版本</strong>：3.8+</li>
                            <li><strong>包管理</strong>：pip + requirements.txt</li>
                            <li><strong>虚拟环境</strong>：venv</li>
                            <li><strong>代码格式化</strong>：autopep8</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🌐 前端环境</h4>
                        <ul>
                            <li><strong>HTML5</strong>：语义化标记</li>
                            <li><strong>CSS3</strong>：Flexbox + Grid布局</li>
                            <li><strong>JavaScript</strong>：ES6+语法</li>
                            <li><strong>HTTP服务器</strong>：Python SimpleHTTPServer</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔧 配置管理</h4>
                        <ul>
                            <li><strong>环境变量</strong>：.env文件</li>
                            <li><strong>API密钥</strong>：安全存储</li>
                            <li><strong>日志配置</strong>：loguru</li>
                            <li><strong>CORS配置</strong>：Flask-CORS</li>
                        </ul>
                    </div>
                </div>

                <h3>5.2 核心模块实现</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>🏗️ 基础架构实现</h4>
                        <ul>
                            <li><strong>AgentBase抽象类</strong>：统一智能体接口和功能</li>
                            <li><strong>Flask应用框架</strong>：RESTful API服务</li>
                            <li><strong>会话管理系统</strong>：多用户会话状态管理</li>
                            <li><strong>日志系统</strong>：统一日志记录和错误追踪</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>🤖 智能体系统实现</h4>
                        <ul>
                            <li><strong>场景智能体</strong>：婚姻、合同、工伤专业咨询</li>
                            <li><strong>对话智能体</strong>：通用法律问答</li>
                            <li><strong>学习智能体</strong>：法律知识学习</li>
                            <li><strong>推荐智能体</strong>：律师团队推荐</li>
                            <li><strong>检索智能体</strong>：案例智能检索</li>
                            <li><strong>生成智能体</strong>：法律文书生成</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>🎨 前端界面实现</h4>
                        <ul>
                            <li><strong>响应式布局</strong>：适配多种屏幕尺寸</li>
                            <li><strong>交互式对话</strong>：类似微信的聊天界面</li>
                            <li><strong>功能模块切换</strong>：Tab页面导航</li>
                            <li><strong>实时API通信</strong>：AJAX异步请求</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>🔌 API接口实现</h4>
                        <ul>
                            <li><strong>健康检查接口</strong>：系统状态监控</li>
                            <li><strong>配置管理接口</strong>：API密钥动态配置</li>
                            <li><strong>智能体通信接口</strong>：统一的对话API</li>
                            <li><strong>文件下载接口</strong>：生成文档下载</li>
                        </ul>
                    </div>
                </div>

                <h3>5.3 关键技术实现细节</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>🧠 AI模型集成</h4>
                        <ul>
                            <li>通义千问大模型API调用</li>
                            <li>LangChain框架集成</li>
                            <li>提示词工程优化</li>
                            <li>流式输出处理</li>
                            <li>上下文管理</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>📊 推荐算法实现</h4>
                        <ul>
                            <li>jieba中文分词</li>
                            <li>TF-IDF特征提取</li>
                            <li>余弦相似度计算</li>
                            <li>多维度权重调整</li>
                            <li>结果排序优化</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>📄 文档生成实现</h4>
                        <ul>
                            <li>python-docx库应用</li>
                            <li>模板化文档结构</li>
                            <li>动态内容填充</li>
                            <li>格式化处理</li>
                            <li>文件管理系统</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🔍 数据处理实现</h4>
                        <ul>
                            <li>JSON数据解析</li>
                            <li>网络API调用</li>
                            <li>数据缓存机制</li>
                            <li>错误处理策略</li>
                            <li>数据验证逻辑</li>
                        </ul>
                    </div>
                </div>

                <h3>5.4 代码质量保证</h3>
                <div class="highlight-box">
                    <h4>编码规范与质量控制</h4>
                    <ul>
                        <li><strong>PEP8规范</strong>：严格遵循Python编码规范</li>
                        <li><strong>代码注释</strong>：详细的函数和类注释</li>
                        <li><strong>异常处理</strong>：完善的错误处理机制</li>
                        <li><strong>日志记录</strong>：关键操作日志追踪</li>
                        <li><strong>模块化设计</strong>：高内聚低耦合的模块结构</li>
                    </ul>
                </div>
            </div>

            <!-- 6. 项目测试 -->
            <div class="section">
                <h2>六、项目测试</h2>

                <h3>6.1 测试策略</h3>
                <div class="architecture-diagram">
                    <h4>测试金字塔模型</h4>
                    <div class="code-block">
                        E2E测试 (端到端测试)
                    ↗                    ↖
            集成测试                    API测试
        ↗                                    ↖
    单元测试                                功能测试
↗                                                ↖
代码审查                                        性能测试
                    </div>
                </div>

                <h3>6.2 测试类型与方法</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>🔬 单元测试</h4>
                        <ul>
                            <li><strong>测试框架</strong>：pytest</li>
                            <li><strong>覆盖率工具</strong>：coverage.py</li>
                            <li><strong>测试范围</strong>：核心算法函数</li>
                            <li><strong>测试数据</strong>：模拟数据集</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔗 集成测试</h4>
                        <ul>
                            <li><strong>API测试</strong>：Postman + Newman</li>
                            <li><strong>数据库测试</strong>：数据一致性验证</li>
                            <li><strong>第三方服务</strong>：模拟外部API</li>
                            <li><strong>组件交互</strong>：模块间通信测试</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🌐 功能测试</h4>
                        <ul>
                            <li><strong>用户界面测试</strong>：手动功能验证</li>
                            <li><strong>业务流程测试</strong>：端到端场景</li>
                            <li><strong>兼容性测试</strong>：多浏览器支持</li>
                            <li><strong>响应式测试</strong>：多设备适配</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>⚡ 性能测试</h4>
                        <ul>
                            <li><strong>负载测试</strong>：并发用户模拟</li>
                            <li><strong>压力测试</strong>：系统极限测试</li>
                            <li><strong>响应时间</strong>：API响应速度</li>
                            <li><strong>资源监控</strong>：内存和CPU使用</li>
                        </ul>
                    </div>
                </div>

                <h3>6.3 具体测试实施</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>🧪 推荐算法测试</h4>
                        <ul>
                            <li>特征提取准确性测试</li>
                            <li>相似度计算正确性验证</li>
                            <li>推荐结果质量评估</li>
                            <li>边界条件处理测试</li>
                        </ul>
                        <div class="code-block">
# 推荐算法测试示例
def test_lawyer_recommendation():
    agent = LawyerRecommendationAgent()
    test_requests = [
        "我需要处理离婚财产分割问题，希望找北京的专业律师",
        "公司合同纠纷，需要上海的律师团队"
    ]
    for request in test_requests:
        response = agent.process_recommendation_request(request)
        assert "律师团队推荐结果" in response
                        </div>
                    </div>
                    <div class="timeline-item">
                        <h4>📄 文书生成测试</h4>
                        <ul>
                            <li>模板选择功能测试</li>
                            <li>信息收集流程测试</li>
                            <li>文档生成质量验证</li>
                            <li>文件下载功能测试</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>🔍 案例检索测试</h4>
                        <ul>
                            <li>关键词搜索准确性</li>
                            <li>罪名分类正确性</li>
                            <li>搜索结果相关性</li>
                            <li>异常情况处理</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>🌐 API接口测试</h4>
                        <ul>
                            <li>请求响应格式验证</li>
                            <li>错误状态码处理</li>
                            <li>参数验证测试</li>
                            <li>并发请求测试</li>
                        </ul>
                    </div>
                </div>

                <h3>6.4 测试结果与优化</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>✅ 测试通过率</h4>
                        <ul>
                            <li>单元测试覆盖率：85%+</li>
                            <li>API接口测试：100%通过</li>
                            <li>功能测试：95%+通过</li>
                            <li>性能测试：满足预期指标</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🐛 问题发现与修复</h4>
                        <ul>
                            <li>推荐算法精度优化</li>
                            <li>API响应时间优化</li>
                            <li>前端交互体验改进</li>
                            <li>错误处理机制完善</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>📊 性能指标</h4>
                        <ul>
                            <li>平均响应时间：< 2秒</li>
                            <li>并发处理能力：50+用户</li>
                            <li>系统稳定性：99%+</li>
                            <li>内存使用：< 512MB</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h4>🔄 持续改进</h4>
                        <ul>
                            <li>自动化测试流程</li>
                            <li>性能监控告警</li>
                            <li>用户反馈收集</li>
                            <li>迭代优化计划</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 7. 项目部署 -->
            <div class="section">
                <h2>七、项目部署</h2>

                <h3>7.1 硬件环境要求</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>💻 开发环境</h4>
                        <ul>
                            <li><strong>CPU</strong>：Intel i5 8代+ / AMD Ryzen 5+</li>
                            <li><strong>内存</strong>：8GB RAM（推荐16GB）</li>
                            <li><strong>存储</strong>：256GB SSD可用空间</li>
                            <li><strong>网络</strong>：稳定的互联网连接</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🖥️ 生产环境</h4>
                        <ul>
                            <li><strong>CPU</strong>：4核心+ 2.4GHz+</li>
                            <li><strong>内存</strong>：4GB RAM（推荐8GB）</li>
                            <li><strong>存储</strong>：50GB可用磁盘空间</li>
                            <li><strong>带宽</strong>：100Mbps+网络带宽</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>☁️ 云服务器推荐</h4>
                        <ul>
                            <li><strong>阿里云</strong>：ECS 2核4GB配置</li>
                            <li><strong>腾讯云</strong>：CVM 2核4GB配置</li>
                            <li><strong>华为云</strong>：ECS 2核4GB配置</li>
                            <li><strong>AWS</strong>：t3.medium实例</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔧 系统要求</h4>
                        <ul>
                            <li><strong>操作系统</strong>：Windows 10+ / macOS 10.15+ / Ubuntu 18.04+</li>
                            <li><strong>Python版本</strong>：3.8+ (推荐3.9)</li>
                            <li><strong>浏览器</strong>：Chrome 90+ / Firefox 88+ / Safari 14+</li>
                            <li><strong>端口要求</strong>：5000(API) + 8000(Web)</li>
                        </ul>
                    </div>
                </div>

                <h3>7.2 软件环境配置</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>🐍 Python环境安装</h4>
                        <div class="code-block">
# 1. 安装Python 3.8+
# Windows: 从python.org下载安装包
# macOS: brew install python3
# Ubuntu: sudo apt-get install python3 python3-pip

# 2. 创建虚拟环境
python -m venv lca_env

# 3. 激活虚拟环境
# Windows: lca_env\Scripts\activate
# macOS/Linux: source lca_env/bin/activate

# 4. 升级pip
python -m pip install --upgrade pip
                        </div>
                    </div>
                    <div class="timeline-item">
                        <h4>📦 依赖包安装</h4>
                        <div class="code-block">
# 1. 克隆项目代码
git clone https://github.com/your-repo/LegalConsultationAssistant.git
cd LegalConsultationAssistant

# 2. 安装项目依赖
pip install -r requirements.txt

# 3. 验证安装
python -c "import flask, langchain, jieba; print('依赖安装成功')"
                        </div>
                    </div>
                    <div class="timeline-item">
                        <h4>🔑 API密钥配置</h4>
                        <div class="code-block">
# 1. 创建环境变量文件
touch .env

# 2. 配置API密钥
echo "Qwen_API_KEY=your_qwen_api_key_here" >> .env
echo "OPENAI_API_KEY=your_openai_api_key_here" >> .env
echo "OPENAI_API_BASE=https://api.openai.com/v1" >> .env

# 3. 验证配置
python -c "from dotenv import load_dotenv; import os; load_dotenv(); print('API密钥配置完成')"
                        </div>
                    </div>
                    <div class="timeline-item">
                        <h4>📁 目录结构检查</h4>
                        <div class="code-block">
LegalConsultationAssistant/
├── agents/                 # 智能体模块
├── api/                   # API接口
├── web/                   # 前端文件
├── utils/                 # 工具模块
├── prompts/               # 提示词文件
├── data/                  # 数据文件
├── logs/                  # 日志目录
├── generated_docs/        # 生成文档目录
├── requirements.txt       # 依赖列表
├── main_html.py          # 主启动文件
└── .env                  # 环境变量
                        </div>
                    </div>
                </div>

                <h3>7.3 部署步骤</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>🚀 本地部署</h4>
                        <ol>
                            <li><strong>环境准备</strong>：安装Python和依赖包</li>
                            <li><strong>配置API密钥</strong>：设置.env文件</li>
                            <li><strong>启动服务</strong>：运行main_html.py</li>
                            <li><strong>访问测试</strong>：浏览器打开localhost:8000</li>
                        </ol>
                        <div class="code-block">
# 启动命令
python main_html.py

# 访问地址
# Web界面: http://localhost:8000
# API服务: http://localhost:5000
                        </div>
                    </div>
                    <div class="feature-item">
                        <h4>☁️ 云服务器部署</h4>
                        <ol>
                            <li><strong>服务器配置</strong>：购买并配置云服务器</li>
                            <li><strong>环境安装</strong>：安装Python和Git</li>
                            <li><strong>代码部署</strong>：克隆代码并安装依赖</li>
                            <li><strong>服务启动</strong>：使用systemd管理服务</li>
                        </ol>
                        <div class="code-block">
# 创建systemd服务文件
sudo nano /etc/systemd/system/lca.service

[Unit]
Description=LCA Legal Consultation Assistant
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/LegalConsultationAssistant
ExecStart=/home/<USER>/lca_env/bin/python main_html.py
Restart=always

[Install]
WantedBy=multi-user.target
                        </div>
                    </div>
                    <div class="feature-item">
                        <h4>🐳 Docker部署</h4>
                        <ol>
                            <li><strong>Dockerfile编写</strong>：创建容器镜像</li>
                            <li><strong>镜像构建</strong>：docker build命令</li>
                            <li><strong>容器运行</strong>：docker run启动</li>
                            <li><strong>服务管理</strong>：docker-compose编排</li>
                        </ol>
                        <div class="code-block">
# Dockerfile示例
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000 8000
CMD ["python", "main_html.py"]

# 构建和运行
docker build -t lca-app .
docker run -p 5000:5000 -p 8000:8000 lca-app
                        </div>
                    </div>
                </div>

                <h3>7.4 部署测试验证</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>🔍 功能测试</h4>
                        <ul>
                            <li><strong>服务启动</strong>：验证API和Web服务正常启动</li>
                            <li><strong>接口测试</strong>：测试所有API接口响应</li>
                            <li><strong>功能验证</strong>：验证六大核心功能模块</li>
                            <li><strong>数据流测试</strong>：端到端数据流验证</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>⚡ 性能测试</h4>
                        <ul>
                            <li><strong>响应时间</strong>：API响应时间< 3秒</li>
                            <li><strong>并发测试</strong>：50+并发用户支持</li>
                            <li><strong>资源监控</strong>：CPU和内存使用率</li>
                            <li><strong>稳定性测试</strong>：长时间运行稳定性</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>🔒 安全测试</h4>
                        <ul>
                            <li><strong>API安全</strong>：接口安全性验证</li>
                            <li><strong>数据保护</strong>：敏感信息保护测试</li>
                            <li><strong>访问控制</strong>：权限控制验证</li>
                            <li><strong>漏洞扫描</strong>：安全漏洞检测</li>
                        </ul>
                    </div>
                    <div class="timeline-item">
                        <h4>🌐 兼容性测试</h4>
                        <ul>
                            <li><strong>浏览器兼容</strong>：多浏览器测试</li>
                            <li><strong>设备适配</strong>：PC、平板、手机测试</li>
                            <li><strong>网络环境</strong>：不同网络条件测试</li>
                            <li><strong>操作系统</strong>：多OS环境验证</li>
                        </ul>
                    </div>
                </div>

                <h3>7.5 运维监控</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <h4>📊 监控指标</h4>
                        <ul>
                            <li><strong>系统指标</strong>：CPU、内存、磁盘使用率</li>
                            <li><strong>应用指标</strong>：API响应时间、错误率</li>
                            <li><strong>业务指标</strong>：用户访问量、功能使用率</li>
                            <li><strong>日志监控</strong>：错误日志、访问日志分析</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🚨 告警机制</h4>
                        <ul>
                            <li><strong>服务异常</strong>：服务停止或崩溃告警</li>
                            <li><strong>性能异常</strong>：响应时间超阈值告警</li>
                            <li><strong>资源异常</strong>：资源使用率过高告警</li>
                            <li><strong>业务异常</strong>：API调用失败率告警</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🔄 备份策略</h4>
                        <ul>
                            <li><strong>代码备份</strong>：Git版本控制和远程仓库</li>
                            <li><strong>数据备份</strong>：定期备份律师数据和用户数据</li>
                            <li><strong>配置备份</strong>：系统配置文件备份</li>
                            <li><strong>日志备份</strong>：重要日志文件归档</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <h4>🛠️ 维护计划</h4>
                        <ul>
                            <li><strong>定期更新</strong>：依赖包和安全补丁更新</li>
                            <li><strong>性能优化</strong>：定期性能分析和优化</li>
                            <li><strong>功能迭代</strong>：根据用户反馈持续改进</li>
                            <li><strong>文档维护</strong>：技术文档和用户手册更新</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 项目总结 -->
            <div class="section">
                <div class="highlight-box" style="text-align: center; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
                    <h3 style="color: white; margin-bottom: 15px;">🎯 项目总结</h3>
                    <p style="font-size: 1.1em; margin: 0;">
                        LCA法律咨询助手系统是一个集成了现代AI技术、Web开发技术和法律专业知识的综合性项目。
                        通过本项目的实施，不仅能够构建一个实用的法律咨询平台，更重要的是在实践中掌握
                        人工智能应用开发的完整流程，提升软件工程和项目管理能力，为未来的技术发展奠定坚实基础。
                    </p>
                    <br>
                    <p style="font-size: 1.0em; margin: 0; opacity: 0.9;">
                        <strong>核心成果：</strong>六大智能体模块 | 基于内容的推荐算法 | 专业法律文书生成 |
                        智能案例检索 | 多场景法律咨询 | 系统化法律学习
                    </p>
                    <br>
                    <p style="font-size: 0.9em; margin: 0; opacity: 0.8;">
                        本项目展现了AI技术在法律服务领域的巨大潜力，为法律科技的发展提供了有价值的实践经验和技术参考。
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
