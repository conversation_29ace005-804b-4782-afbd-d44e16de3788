#!/usr/bin/env python3
"""
测试AI推荐功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_ai_recommendation():
    """测试AI推荐功能"""
    print("=== 测试AI推荐功能 ===")
    
    try:
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        
        # 创建推荐代理
        agent = LawyerRecommendationAgent()
        print("✓ LawyerRecommendationAgent 创建成功")
        
        # 测试不同类型的法律需求
        test_cases = [
            {
                "request": "我需要处理离婚财产分割问题，涉及房产和股权，希望找北京的专业律师",
                "description": "婚姻家庭法 + 财产分割 + 北京地区"
            },
            {
                "request": "公司被起诉商标侵权，需要知识产权律师进行辩护",
                "description": "知识产权法 + 商标侵权 + 诉讼辩护"
            },
            {
                "request": "员工工伤事故，需要劳动法律师处理赔偿问题",
                "description": "劳动法 + 工伤赔偿"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试案例 {i}: {test_case['description']} ---")
            print(f"用户需求: {test_case['request']}")
            
            try:
                # 处理推荐请求
                response = agent.process_recommendation_request(
                    test_case['request'], 
                    f"test_ai_session_{i}"
                )
                
                # 检查响应内容
                if "AI智能推荐" in response:
                    print("✓ 包含AI推荐内容")
                else:
                    print("✗ 缺少AI推荐内容")
                
                if "数据库匹配推荐" in response:
                    print("✓ 包含数据库匹配内容")
                else:
                    print("✗ 缺少数据库匹配内容")
                
                if "🤖" in response:
                    print("✓ 包含AI推荐标识")
                else:
                    print("✗ 缺少AI推荐标识")
                
                # 显示推荐结果摘要
                lines = response.split('<br>')
                summary_lines = []
                for line in lines[:15]:  # 显示前15行
                    if line.strip() and not line.strip().startswith('📝'):
                        summary_lines.append(line.strip())
                
                print("推荐结果摘要:")
                for line in summary_lines[:8]:  # 显示前8行摘要
                    print(f"  {line}")
                if len(summary_lines) > 8:
                    print("  ...")
                
                print(f"✓ 测试案例 {i} 完成")
                
            except Exception as e:
                print(f"✗ 测试案例 {i} 失败: {str(e)}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"✗ AI推荐功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_recommendation_components():
    """测试AI推荐组件"""
    print("\n=== 测试AI推荐组件 ===")
    
    try:
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        
        agent = LawyerRecommendationAgent()
        
        # 测试AI推荐方法
        print("测试 get_ai_recommended_teams 方法...")
        ai_teams = agent.get_ai_recommended_teams(
            "需要处理合同纠纷", 
            "合同法", 
            "上海", 
            "合同纠纷",
            "test_component_session"
        )
        
        print(f"AI推荐团队数量: {len(ai_teams)}")
        
        if ai_teams:
            for i, team in enumerate(ai_teams, 1):
                print(f"{i}. {team.get('name', 'N/A')}")
                print(f"   地点: {team.get('location', 'N/A')}")
                print(f"   专业: {', '.join(team.get('specialties', []))}")
                print(f"   来源: {team.get('source', 'N/A')}")
                print(f"   AI推荐: {team.get('ai_recommended', False)}")
        
        # 测试综合格式化方法
        print("\n测试 format_comprehensive_recommendation 方法...")
        
        # 模拟数据库团队
        db_teams = [
            {
                "id": "db_1",
                "name": "测试数据库律师事务所",
                "location": "北京",
                "specialties": ["合同法"],
                "rating": 4.5,
                "experience_years": 15,
                "team_size": 100,
                "description": "测试数据库律师事务所",
                "contact": {"phone": "010-12345678"},
                "source": "数据库匹配",
                "ai_recommended": False
            }
        ]
        
        # 合并AI推荐和数据库团队
        all_teams = db_teams + ai_teams
        
        formatted_result = agent.format_comprehensive_recommendation(
            all_teams, len(db_teams), len(ai_teams)
        )
        
        print("格式化结果预览:")
        lines = formatted_result.split('<br>')
        for line in lines[:10]:
            if line.strip():
                print(f"  {line.strip()}")
        if len(lines) > 10:
            print("  ...")
        
        return True
        
    except Exception as e:
        print(f"✗ AI推荐组件测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试AI推荐功能...")
    
    tests = [
        ("AI推荐功能", test_ai_recommendation),
        ("AI推荐组件", test_ai_recommendation_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"运行测试: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {str(e)}")
    
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print('='*60)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！AI推荐功能正常工作。")
    else:
        print("⚠️  部分测试失败，请检查AI推荐功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n测试{'成功' if success else '失败'}")
